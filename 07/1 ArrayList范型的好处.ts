// 【TS 泛型类】TypeScript 泛型重构简易版的Java 的 ArrayList 【透彻理解为什么一定要用泛型类】
// 为什么要用泛型类【 好处】
// 好处1:编译期对类上调用方法或属性时的泛型类型进行安全检查(类型安全检査)，不符合泛型实际参数类型(泛型实参类型)就编译通不过，
// 防止不符合条件的数据增加进来。
// 好处2:一种泛型类型被具体化成某种数据类型后，该数据类型的变量获取属性和方法时会有自动提示，这无疑提高代码开发效率和减少出错率。
// 放置讲完后编码的区域

// 如果数组想添加string类型的就会有问题，需要使用泛型解决
class ArrayList {
  public index: number = 0;
  public element: Array<Object>;
  // //第一步：定义一个引用属性【数组】
  constructor() {
    this.element = new Array();
  }

  // 第二步：定义一个方法，用来获取数组中的元素
  get(index: number) {
    return this.element[index];
  }

  // 第三步：定义一个方法，用来删除数组中的元素
  remove(index: number): number;
  remove(element: object): object;
  remove(value: number | object): number | object {
    this.element = this.element.filter((element, index) => {
      if (typeof value === "number") {
        return index !== value;
      } else {
        return element !== value;
      }
    });
    return value;
  }

  // 第四步：定义一个方法，用来添加元素到数组中
  addToEnd(element: object): void {
    this.element.push(element);
  }

  // 第五步：定义一个方法，用来显示数组中的元素
  display(): void {
    this.element.forEach((element, index) => {
      console.log(`index: ${index}, element: ${JSON.stringify(element)}`);
    });
  }

  add(ele: object) {
    if (this.index < 0) {
      throw new Error("数组越界");
    }

    this.element[this.index++] = ele;
  }
}

let stuOne = { name: "张三", age: 18 };
let stuTwo = { name: "李四", age: 20 };
let stuThree = { name: "王五", age: 22 };

let arryList = new ArrayList();
arryList.add(stuOne);
arryList.add(stuTwo);
arryList.add(stuThree);

arryList.display();

const oneElement = arryList.get(0);
console.log(oneElement);

export {};
