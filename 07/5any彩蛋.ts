class Student {
  constructor(public name: string, public age: number) {}
}

class Customer {
  constructor(public custname: string, public age: number) {}
}

let stuone = new Student("张三", 18);

let wangwuCust = new Customer("王五", 18);

let lisiCust = new Customer("李四", 18);

// 利用any 判断是否为Customer类型
function isCustomer(data: any): data is Customer {
  return Boolean(data && data.custname && data.age);
}

console.log(isCustomer(stuone));
console.log(isCustomer(stuone) ? stuone.custname : undefined);

console.log(isCustomer(wangwuCust));
console.log(isCustomer(stuone));

export {};
