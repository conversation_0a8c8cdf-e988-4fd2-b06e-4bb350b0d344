/**
 * 
 * 
 * 【TS 泛型类】详细讲解+透彻总结 any 为什么不能替代类上的泛型?3-6
原因一:编译期间 any 无法进行类型安全检查，而泛型在编译期间可以进行类型安全检查我们学过:any 是所有类型的父类，
也是所有类型的子类如果我们现在是一个宠物店类，希望只能添加 Dog类，
当调用add 方法添加 Customer、student 类必定出现编译错误，从而保证了类型安全检查，
但是 any 类型无法保证类型安全检查，可以为任意类型，包括 string,number，boolean，null，undefined,never,void，unknown 
基础数据类型和数组，类，接口类型，type类型的变量全部能接受，不会进行无法进行类型
安全检查。

原因二:any扩大数据类型的属性后没有编译错误导致潜在错误风险，
而泛型却有效的避免了此类问题发生any 类型可以获取任何属性和任意方法而不会出现编译错误，
因为any可以代表任意数据类型来获取任意属性和任意方法，但是泛型类型被具体化成某种数据类型后，
该数据类型的变量调用该数据类型之外的属性和方法时，出现编译错误，这也减少了代码隐藏潜在错误的风险。

原因三: any 类型数据获取属性和方法时无自动提示，泛型有自动提示
彩蛋:any 类型可以获取任何属性和任意方法而不会出现编译错误，
因为any可以代表任意数据类型的任意属性和任意方法:【 any 的这个特性是一把双刃剑，当我们需要这么使用，它给我们带来方便，但是大多都。况点KIB们E西/做的
 */

class ArrayList {
  public index: number = 0;
  public element: Array<any>;
  constructor() {
    this.element = new Array();
  }

  get(index: number) {
    return this.element[index];
  }

  remove(index: number): number;
  remove(element: any): any;
  remove(value: number | any): number | any {
    this.element = this.element.filter((element, index) => {
      if (typeof value === "number") {
        return index !== value;
      } else {
        return element !== value;
      }
    });
    return value;
  }

  addToEnd(element: any): void {
    this.element.push(element);
  }

  display(): void {
    this.element.forEach((element, index) => {
      console.log(`index: ${index}, element: ${JSON.stringify(element)}`);
    });
  }

  add(ele: any) {
    if (this.index < 0) {
      throw new Error("数组越界");
    }

    this.element[this.index++] = ele;
  }
}

let arrayList = new ArrayList();
arrayList.add(1);
arrayList.add("2");
arrayList.add(true);
arrayList.add(null);
arrayList.add(undefined);
arrayList.add(() => {});
arrayList.add({});
arrayList.add([]);
arrayList.add(new Date());
arrayList.display();

// arrayList.get(0).name // 点不出来
const cust = arrayList.get(0);
cust.buy(); // buy只有在运行时才会报错

export {};
