// 2. 无法进行类型关联和约束 (No Type Association or Constraints)
// 泛型不仅能让你定义一个占位符类型，还能让你在多个地方使用同一个泛型类型参数，
// 从而在不同部分之间建立类型关联。此外，泛型还可以
// 通过 extends 关键字来添加类型约束，
// 确保传入的类型符合特定的条件（比如必须实现某个接口，或者必须是某个类的子类）

// 泛型可以建立类型关联：key 和 value 的类型是关联的
class KeyValueStore<K, V> {
  private store = new Map<K, V>();

  set(key: K, value: V) {
    this.store.set(key, value);
  }

  get(key: K): V | undefined {
    return this.store.get(key);
  }
}

const userStore = new KeyValueStore<number, { name: string }>();
userStore.set(1, { name: "Alice" });
// userStore.set("2", { name: "<PERSON>" }); // 报错：'string' 类型不能赋给 'number' 类型

const retrievedUser = userStore.get(1); // retrievedUser 的类型是 { name: string } | undefined

// 泛型可以添加类型约束：确保 T 必须有 id 属性
interface HasId {
  id: number;
}
class ItemProcessor<T extends HasId> {
  // T 必须是 HasId 的子类型
  process(item: T) {
    console.log(`Processing item with ID: ${item.id}`);
  }
}

const processor = new ItemProcessor<{ id: number; name: string }>();
processor.process({ id: 101, name: "Gizmo" });

// const invalidProcessor = new ItemProcessor<{ name: string }>(); // 报错：类型参数“{ name: string; }”不能赋给类型约束“HasId”

// 如果使用 object：

// 使用 object：无法建立类型关联
class KeyValueStoreObject {
  // key 和 value 都是 object，它们之间没有类型关联
  private store = new Map<object, object>();

  set(key: object, value: object) {
    this.store.set(key, value);
  }

  get(key: object): object | undefined {
    return this.store.get(key);
  }
}

const objStore = new KeyValueStoreObject();
objStore.set("user_id_1", { name: "Alice" }); //  类型“string”的参数不能赋给类型“object”的参数。ts(2345)

export {};
