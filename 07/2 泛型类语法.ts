// 【TS 泛型类】TypeScript 泛型重构简易版的Java 的 ArrayList 【透彻理解为什么一定要用泛型类】
// 为什么要用泛型类【 好处】
// 好处1:编译期对类上调用方法或属性时的泛型类型进行安全检查(类型安全检査)，不符合泛型实际参数类型(泛型实参类型)就编译通不过，
// 防止不符合条件的数据增加进来。
// 好处2:一种泛型类型被具体化成某种数据类型后，该数据类型的变量获取属性和方法时会有自动提示，这无疑提高代码开发效率和减少出错率。
// 放置讲完后编码的区域

/**
 * 
[TS 泛型类] 泛型类定义+格式 
【究竟该怎样给泛型下定义才精准无偏差?】
*泛型一种参数化数据类型，
具有以下特点的数据类型叫泛型:【
特点一:定义时不明确,使用时必须明确成某种具体数据类型的数据类型。 泛型的宽泛】
特点二:编译期间进行数据类型安全检查的数据类型。【泛型的严谨】

特别注意:
1.类型安全检查发生在编译期间
2.泛型是参数化的数据类型，使用时明确化后的数据类型就是参数的值
 */

// 泛型类语法

/**
 * 
class 类名<泛型形参类型> 泛型形参必须是A-Z任何一个字符如下:A-Z

class ArrayList<T> {
   array: Array<T>;
   add(element: T): void {
      this.array.push(element);
   }
}
 

 * 
 */

// 泛型的any化
// 泛型的默认值的问题
// <T= any>
// <T={}>
class ArrayList<T> {
  public index: number = 0;
  public element: Array<T>;
  // //第一步：定义一个引用属性【数组】
  constructor() {
    this.element = new Array();
  }

  // 第二步：定义一个方法，用来获取数组中的元素
  get(index: number): T {
    return this.element[index];
  }

  // 第三步：定义一个方法，用来删除数组中的元素
  remove(index: number): number;
  remove(element: T): T;
  remove(value: number | T): number | T {
    this.element = this.element.filter((element, index) => {
      if (typeof value === "number") {
        return index !== value;
      } else {
        return element !== value;
      }
    });
    return value;
  }

  // 第四步：定义一个方法，用来添加元素到数组中
  addToEnd(element: T): void {
    this.element.push(element);
  }

  // 第五步：定义一个方法，用来显示数组中的元素
  display(): void {
    this.element.forEach((element, index) => {
      console.log(`index: ${index}, element: ${JSON.stringify(element)}`);
    });
  }

  add(ele: T) {
    if (this.index < 0) {
      throw new Error("数组越界");
    }

    this.element[this.index++] = ele;
  }
}

let stuOne = { name: "张三", age: 18 };
let stuTwo = { name: "李四", age: 20 };
let stuThree = { name: "王五", age: 22 };
console.log("-----", typeof stuOne);

let arryList = new ArrayList<object>();

arryList.add(stuOne);
arryList.add(stuTwo);
arryList.add(stuThree);

arryList.display();

const oneElement = arryList.get(0);
console.log(oneElement);

let arrayString = new ArrayList<string>();
arrayString.add("张三");
arrayString.add("李四");
arrayString.add("王五");

arrayString.display();

let arrayNumber = new ArrayList<number>();
arrayNumber.add(1);
arrayNumber.add(2);
arrayNumber.add(3);

arrayNumber.display();

let arrayList2 = new ArrayList<typeof stuOne>();
arrayList2.add(stuOne);
arrayList2.add(stuTwo);
arrayList2.add(stuThree);

arrayList2.display();

let arrayList3 = new ArrayList<number | string>();
arrayList3.add(1);
arrayList3.add(2);
arrayList3.add(3);
arrayList3.add("4");
arrayList3.add("5");
arrayList3.add("6");

arrayList3.display();

type StuType = {
  name: string;
  age: number;
};

let arrayList4 = new ArrayList<StuType>();
arrayList4.add(stuOne);
arrayList4.add(stuTwo);
arrayList4.add(stuThree);

arrayList4.display();

interface StuInter {
  name: string;
  age: number;
}

let arrayList5 = new ArrayList<StuInter>();
arrayList5.add(stuOne);
arrayList5.add(stuTwo);
arrayList5.add(stuThree);

arrayList5.display();

export {};
