import { ArrayList } from "./arrayList";

export default class Pager {
  firstRcordNoCurPage!: number; // 每一页的第一条记录号是多少
  pageSize: number = 3; // 每一页显示多少条记录
  pageNo: number = 0; // 当前第几页
  dataList!: ArrayList; // 封装数据表取出来的全部数据的集合类

  constructor(pageNo: number) {
    this.pageNo = pageNo;
  }

  // 显示当前页的数据
  public showCurrentPageData() {
    this.firstRcordNoCurPage = this.getFirstRcordNoCurPage();
    const lastRecordNoCurPage = this.firstRcordNoCurPage + this.pageSize;

    return this.dataList.element.slice(
      this.firstRcordNoCurPage,
      lastRecordNoCurPage
    );
  }

  public getFirstRcordNoCurPage() {
    return (this.pageNo - 1) * this.pageSize;
  }
}
