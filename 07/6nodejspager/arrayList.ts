export class ArrayList {
  public index: number = 0;
  public element: Array<object>;
  constructor() {
    this.element = new Array();
  }

  get(index: number) {
    return this.element[index];
  }

  remove(index: number): number;
  remove(element: object): object;
  remove(value: number | object): number | object {
    this.element = this.element.filter((element, index) => {
      if (typeof value === "number") {
        return index !== value;
      } else {
        return element !== value;
      }
    });
    return value;
  }

  addToEnd(element: object): void {
    this.element.push(element);
  }

  display(): void {
    this.element.forEach((element, index) => {
      console.log(`index: ${index}, element: ${JSON.stringify(element)}`);
    });
  }

  add(ele: object) {
    if (this.index < 0) {
      throw new Error("数组越界");
    }

    this.element[this.index++] = ele;
  }
}
