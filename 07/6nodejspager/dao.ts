import { ArrayList } from "./arrayList";
import { Food } from "./entity";

export class FoodDao {
  arrayListFood!: ArrayList;

  init() {
    this.arrayListFood = new ArrayList();
    this.arrayListFood.add(new Food("1", "肯德基", "鸡米花"));
    this.arrayListFood.add(new Food("2", "麦当劳", "薯条"));
    this.arrayListFood.add(new Food("3", "汉堡王", "汉堡"));
    this.arrayListFood.add(new Food("4", "必胜客", "披萨"));
    this.arrayListFood.add(new Food("5", " subway", "BLT"));

    return this.arrayListFood;
  }

  findAllFood() {
    return this.init();
  }
}

export class CustomerDao {}
