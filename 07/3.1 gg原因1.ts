//1. 失去类型特异性 (Loss of Type Specificity)
// 当你使用泛型时，你是在告诉 TypeScript 编译器，你希望这个类、函数或接口能够操作任何类型的数据，但同时保留这种类型的具体信息。泛型允许你定义一个占位符类型，然后在实际使用时，这个占位符会被具体的类型（比如 string、number、User 接口等）所填充。
// 使用泛型：T 在使用时会被具体化
class Box<T> {
  private value: T;
  constructor(val: T) {
    this.value = val;
  }
  getValue(): T {
    return this.value;
  }
}

const stringBox = new Box("hello");
let s: string = stringBox.getValue(); // TypeScript 知道 s 是 string
stringBox.getValue().toUpperCase(); // 可以安全地调用 string 方法

const numberBox = new Box(123);
let n: number = numberBox.getValue(); // TypeScript 知道 n 是 number
numberBox.getValue().toFixed(2); // 可以安全地调用 number 方法

// 如果使用 object 代替泛型：

// 使用 object：失去了类型特异性
class BoxObject {
  private value: object; // value 的类型是 object
  constructor(val: object) {
    this.value = val;
  }
  getValue(): object {
    return this.value;
  }
}

// const stringBoxObj = new BoxObject("hello"); // string 类型不能赋值给 object 类型

export {};
