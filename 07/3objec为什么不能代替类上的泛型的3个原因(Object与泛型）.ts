/**
**
3-5 【TS 泛型类】详细讲解+透彻总结 object 为什么不能替代类上的泛型?
原因一:编译期间 object 无法进行类型安全检查，而泛型在编译期间可以进行类型安全检查
object接受也只能接受所有的 object 类型的变量，比如有 Customer、Student、Dog 类的实例都是对象类型
，或者自己定义的对象，都可以传递给 object 类型的方法参数或属性，但如果我们只希望添加Customer类的对象，
当添加其他类的对象必须出现编译错误，但是 object 无法做到，就只能用泛型了。

原因二:object 类型数据无法接受非 object 类型的变量，只能接受 object 类型的变量，泛型能轻松做到
正因为 object 接受也只能接受所有的 object 类型的变量，
那么如果有一个集合类[数组封装类]有一个 add 方法，
允许每次添加指定类型的变量到 add 方法的参数，
比如:我们第一轮的希望添加 10 次字符串类型的变量，第二轮的希望添加10次整数类型变量，
第三轮的希望添加10 次顾客类型的变量，泛型轻松做到。
object类型数据无法接受任意非 object 类型的变量，object 只能接受所有的 object 类型的变量。

原因三:object 类型数据获取属性和方法时无自动提示
-种泛型类型被具体化成某种数据类型后，该数据类型的变量获取属性和方法时会有自动提示，
提高代码开发效率和减少出错率，但在 object 类型的变量无法获取数据类型的属性和方法。降低了休验感和开发效率。
 */

class ArrayList {
  public index: number = 0;
  public element: Array<object>;
  constructor() {
    this.element = new Array();
  }

  get(index: number) {
    return this.element[index];
  }

  remove(index: number): number;
  remove(element: object): object;
  remove(value: number | object): number | object {
    this.element = this.element.filter((element, index) => {
      if (typeof value === "number") {
        return index !== value;
      } else {
        return element !== value;
      }
    });
    return value;
  }

  addToEnd(element: object): void {
    this.element.push(element);
  }

  display(): void {
    this.element.forEach((element, index) => {
      console.log(`index: ${index}, element: ${JSON.stringify(element)}`);
    });
  }

  add(ele: object) {
    if (this.index < 0) {
      throw new Error("数组越界");
    }

    this.element[this.index++] = ele;
  }
}

class Customer {
  constructor(public name: string, public age: number) {}
}

let obj: Object = new Customer("张三", 18);
let obj2: object = new Customer("张三", 18);
console.log(obj.name); // 类型“Object”上不存在属性“name”。ts(2339)

console.log(obj2.name); // 类型“object”上不存在属性“name”。ts(2339)

type studentType = {
  name: string;
  age: number;
};
const stuOne: studentType = { name: "张三", age: 18 };

let custArrayList = new ArrayList();
custArrayList.add(obj);
custArrayList.add(stuOne); // 因为add的形式参数是object类型，但我们只想添加Customer类型的对象，所以object无法替代泛型
