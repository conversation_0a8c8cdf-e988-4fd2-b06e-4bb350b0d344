//在 TypeScript 中，泛型可以设置默认值。
// 这意味着当你在使用泛型类型时，如果没有明确指定类型参数，
// TypeScript 就会使用你预设的默认类型。
// 这在编写更灵活、更易于使用的组件或函数时非常有用。
type MyGenericType<T = string> = T[];

function processData<T = number>(data: T): T {
  // ...
  return data;
}

class Box<T = unknown> {
  value: T;
  constructor(val: T) {
    this.value = val;
  }
}
//MyGenericType 的默认类型是 string。

// processData 函数的默认类型是 number。

// Box 类的默认类型是 unknown。

//1. 类型别名

type ApiResponse<Data = any> = {
  // Data 的默认值是 any
  status: number;
  message: string;
  data: Data;
};

// 使用默认值：Data 会是 any
const defaultResponse: ApiResponse = {
  status: 200,
  message: "Success",
  data: { id: 1, name: "Item" }, // data 的类型是 any
};

// 明确指定 Data 类型为 string[]
const stringArrayResponse: ApiResponse<string[]> = {
  status: 200,
  message: "List of strings",
  data: ["a", "b", "c"], // data 的类型是 string[]
};

// 明确指定 Data 类型为 User
interface User {
  id: number;
  name: string;
}

const userResponse: ApiResponse<User> = {
  status: 200,
  message: "User data",
  data: { id: 123, name: "Alice" }, // data 的类型是 User
};

// 2 函数
function getOrDefault<T = string>(value?: T): T {
  // T 的默认值是 string
  if (value === undefined) {
    return "default value" as T; // 这里需要类型断言，因为返回类型是 T
  }
  return value;
}

// 不指定类型参数，T 默认为 string
let val1 = getOrDefault(); // val1 的类型是 string，值为 "default value"
console.log(val1);

// 明确指定类型参数为 number
let val2 = getOrDefault<number>(123); // val2 的类型是 number，值为 123
console.log(val2);

// 明确指定类型参数为 boolean
let val3 = getOrDefault<boolean>(true); // val3 的类型是 boolean，值为 true
console.log(val3);

// 3. 类（classess)和接口
class Cache<Key = string, Value = any> {
  // Key 默认 string, Value 默认 any
  private data: Map<Key, Value> = new Map();

  set(key: Key, value: Value) {
    this.data.set(key, value);
  }

  get(key: Key): Value | undefined {
    return this.data.get(key);
  }
}

// 使用默认值：Key 是 string, Value 是 any
const myCache = new Cache();
myCache.set("user_id", { name: "Bob", age: 30 });
const userData = myCache.get("user_id"); // userData 类型是 any

// 明确指定类型：Key 是 number, Value 是 string
const stringCache = new Cache<number, string>();
stringCache.set(1, "hello");
stringCache.set(2, "world");
const item = stringCache.get(1); // item 类型是 string
// Cache 类允许你在不指定键和值的类型时，默认使用 string 作为键类型，any 作为值类型，这提供了极大的灵活性。

export {};
