{"version": 3, "sources": ["src/mysetprototypeof.js", "node_modules/parcel-bundler/src/builtins/hmr-runtime.js"], "names": ["_extendsv1", "parent", "middle", "Object", "create", "prototype", "count", "writable", "value", "_extendsv2", "setPrototypeOf", "People", "name", "sex", "phone", "doEat", "console", "log", "ChinesePeople", "language", "apply", "drink", "constructor", "chinesePeople1", "OVERLAY_ID", "OldModule", "module", "bundle", "<PERSON><PERSON><PERSON>", "moduleName", "call", "hot", "data", "hotData", "_acceptCallbacks", "_disposeCallbacks", "accept", "fn", "push", "dispose", "checkedAssets", "assetsToAccept", "isParcelRequire", "WebSocket", "hostname", "location", "protocol", "ws", "onmessage", "event", "JSON", "parse", "type", "handled", "assets", "for<PERSON>ach", "asset", "isNew", "didAccept", "hmrAcceptCheck", "global", "parcelRequire", "id", "every", "generated", "js", "clear", "hmrApply", "v", "hmrAcceptRun", "reload", "close", "onclose", "removeErrorOverlay", "error", "message", "stack", "overlay", "createErrorOverlay", "document", "body", "append<PERSON><PERSON><PERSON>", "getElementById", "remove", "createElement", "stackTrace", "innerText", "innerHTML", "getParents", "modules", "parents", "k", "d", "dep", "Array", "isArray", "length", "concat", "Function", "deps", "cached", "cache", "some", "cb"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAIC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACH,MAAM,CAACI,SAAS,EAAE;IAC3CC,KAAK,EAAE;MACLC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;IACT;EACF,CAAC,CAAC,CAAC,CAAC;EACJ,OAAON,MAAM;AACf;AAEA,SAASO,UAAUA,CAACR,MAAM,EAAE;EAC1B,IAAIC,MAAM,GAAG;IAAEI,KAAK,EAAE;EAAG,CAAC,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,OAAOH,MAAM,CAACO,cAAc,CAACR,MAAM,EAAED,MAAM,CAACI,SAAS,CAAC;AACxD;AAEA,IAAIH,MAAM,GAAGO,UAAU,CAACE,MAAM,CAAC;AAE/B,SAASA,MAAMA,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChC;EACA,IAAI,CAACF,IAAI,GAAGA,IAAI,CAAC,CAAC;EAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;EACd,IAAI,CAACC,KAAK,GAAGA,KAAK;AACpB;AAEAH,MAAM,CAACN,SAAS,CAACU,KAAK,GAAG,YAAY;EACnC;EACAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACL,IAAI,GAAG,OAAO,CAAC;AAClC,CAAC;AAED,SAASM,aAAaA,CAACN,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEK,QAAQ,EAAE;EACjD;EACAR,MAAM,CAACS,KAAK,CAAC,IAAI,EAAE,CAACR,IAAI,EAAEC,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EACxC,IAAI,CAACK,QAAQ,GAAGA,QAAQ;AAC1B;AAEAD,aAAa,CAACb,SAAS,CAACgB,KAAK,GAAG,YAAY;EAC1CL,OAAO,CAACC,GAAG,CAAC,IAAI,CAACL,IAAI,GAAG,OAAO,CAAC;AAClC,CAAC;AAEDM,aAAa,CAACb,SAAS,GAAGH,MAAM;AAChCgB,aAAa,CAACb,SAAS,CAACiB,WAAW,GAAGJ,aAAa,CAAC,CAAC;AACrD,IAAIK,cAAc,GAAG,IAAIL,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,CAAC;AACtEF,OAAO,CAACC,GAAG,CAACM,cAAc,CAAC;AAC3BP,OAAO,CAACC,GAAG,CAACM,cAAc,YAAYZ,MAAM,CAAC,CAAC,CAAC;AAC/CK,OAAO,CAACC,GAAG,CAACM,cAAc,YAAYL,aAAa,CAAC,CAAC,CAAC;;AAEtD;AACA;AACA;AACA;AACAK,cAAc,CAACF,KAAK,CAAC,CAAC;;;AC3DtB,IAAIG,UAAU,GAAG,4BAA4B;AAE7C,IAAIC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACC,MAAM;AAEpC,SAASA,MAAMA,CAACC,UAAU,EAAE;EAC1BJ,SAAS,CAACK,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC;EAChC,IAAI,CAACE,GAAG,GAAG;IACTC,IAAI,EAAEN,MAAM,CAACC,MAAM,CAACM,OAAO;IAC3BC,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,MAAM,EAAE,SAAAA,CAAUC,EAAE,EAAE;MACpB,IAAI,CAACH,gBAAgB,CAACI,IAAI,CAACD,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC;IAClD,CAAC;IACDE,OAAO,EAAE,SAAAA,CAAUF,EAAE,EAAE;MACrB,IAAI,CAACF,iBAAiB,CAACG,IAAI,CAACD,EAAE,CAAC;IACjC;EACF,CAAC;EAEDX,MAAM,CAACC,MAAM,CAACM,OAAO,GAAG,IAAI;AAC9B;AAEAP,MAAM,CAACC,MAAM,CAACC,MAAM,GAAGA,MAAM;AAC7B,IAAIY,aAAa,EAAEC,cAAc;AAEjC,IAAIxC,MAAM,GAAGyB,MAAM,CAACC,MAAM,CAAC1B,MAAM;AACjC,IAAI,CAAC,CAACA,MAAM,IAAI,CAACA,MAAM,CAACyC,eAAe,KAAK,OAAOC,SAAS,KAAK,WAAW,EAAE;EAC5E,IAAIC,QAAQ,GAAG,MAA4BC,QAAQ,CAACD,QAAQ;EAC5D,IAAIE,QAAQ,GAAGD,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI;EAC5D,IAAIC,EAAE,GAAG,IAAIJ,SAAS,CAACG,QAAQ,GAAG,KAAK,GAAGF,QAAQ,GAAG,GAAG,UAAuB,GAAG,GAAG,CAAC;EACtFG,EAAE,CAACC,SAAS,GAAG,UAASC,KAAK,EAAE;IAC7BT,aAAa,GAAG,CAAC,CAAC;IAClBC,cAAc,GAAG,EAAE;IAEnB,IAAIT,IAAI,GAAGkB,IAAI,CAACC,KAAK,CAACF,KAAK,CAACjB,IAAI,CAAC;IAEjC,IAAIA,IAAI,CAACoB,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAIC,OAAO,GAAG,KAAK;MACnBrB,IAAI,CAACsB,MAAM,CAACC,OAAO,CAAC,UAASC,KAAK,EAAE;QAClC,IAAI,CAACA,KAAK,CAACC,KAAK,EAAE;UAChB,IAAIC,SAAS,GAAGC,cAAc,CAACC,MAAM,CAACC,aAAa,EAAEL,KAAK,CAACM,EAAE,CAAC;UAC9D,IAAIJ,SAAS,EAAE;YACbL,OAAO,GAAG,IAAI;UAChB;QACF;MACF,CAAC,CAAC;;MAEF;MACAA,OAAO,GAAGA,OAAO,IAAIrB,IAAI,CAACsB,MAAM,CAACS,KAAK,CAAC,UAASP,KAAK,EAAE;QACrD,OAAOA,KAAK,CAACJ,IAAI,KAAK,KAAK,IAAII,KAAK,CAACQ,SAAS,CAACC,EAAE;MACnD,CAAC,CAAC;MAEF,IAAIZ,OAAO,EAAE;QACXrC,OAAO,CAACkD,KAAK,CAAC,CAAC;QAEflC,IAAI,CAACsB,MAAM,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;UACnCW,QAAQ,CAACP,MAAM,CAACC,aAAa,EAAEL,KAAK,CAAC;QACvC,CAAC,CAAC;QAEFf,cAAc,CAACc,OAAO,CAAC,UAAUa,CAAC,EAAE;UAClCC,YAAY,CAACD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIvB,QAAQ,CAACyB,MAAM,EAAE;QAAE;QAC5BzB,QAAQ,CAACyB,MAAM,CAAC,CAAC;MACnB;IACF;IAEA,IAAItC,IAAI,CAACoB,IAAI,KAAK,QAAQ,EAAE;MAC1BL,EAAE,CAACwB,KAAK,CAAC,CAAC;MACVxB,EAAE,CAACyB,OAAO,GAAG,YAAY;QACvB3B,QAAQ,CAACyB,MAAM,CAAC,CAAC;MACnB,CAAC;IACH;IAEA,IAAItC,IAAI,CAACoB,IAAI,KAAK,gBAAgB,EAAE;MAClCpC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MAExCwD,kBAAkB,CAAC,CAAC;IACtB;IAEA,IAAIzC,IAAI,CAACoB,IAAI,KAAK,OAAO,EAAE;MACzBpC,OAAO,CAAC0D,KAAK,CAAC,eAAe,GAAG1C,IAAI,CAAC0C,KAAK,CAACC,OAAO,GAAG,IAAI,GAAG3C,IAAI,CAAC0C,KAAK,CAACE,KAAK,CAAC;MAE7EH,kBAAkB,CAAC,CAAC;MAEpB,IAAII,OAAO,GAAGC,kBAAkB,CAAC9C,IAAI,CAAC;MACtC+C,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACJ,OAAO,CAAC;IACpC;EACF,CAAC;AACH;AAEA,SAASJ,kBAAkBA,CAAA,EAAG;EAC5B,IAAII,OAAO,GAAGE,QAAQ,CAACG,cAAc,CAAC1D,UAAU,CAAC;EACjD,IAAIqD,OAAO,EAAE;IACXA,OAAO,CAACM,MAAM,CAAC,CAAC;EAClB;AACF;AAEA,SAASL,kBAAkBA,CAAC9C,IAAI,EAAE;EAChC,IAAI6C,OAAO,GAAGE,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAC3CP,OAAO,CAACf,EAAE,GAAGtC,UAAU;;EAEvB;EACA,IAAImD,OAAO,GAAGI,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAC3C,IAAIC,UAAU,GAAGN,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAC9CT,OAAO,CAACW,SAAS,GAAGtD,IAAI,CAAC0C,KAAK,CAACC,OAAO;EACtCU,UAAU,CAACC,SAAS,GAAGtD,IAAI,CAAC0C,KAAK,CAACE,KAAK;EAEvCC,OAAO,CAACU,SAAS,GACf,wNAAwN,GACtN,mFAAmF,GACnF,yEAAyE,GACzE,qEAAqE,GAAGZ,OAAO,CAACY,SAAS,GAAG,QAAQ,GACpG,OAAO,GAAGF,UAAU,CAACE,SAAS,GAAG,QAAQ,GAC3C,QACD;EAED,OAAOV,OAAO;AAEhB;AAEA,SAASW,UAAUA,CAAC7D,MAAM,EAAEmC,EAAE,EAAE;EAC9B,IAAI2B,OAAO,GAAG9D,MAAM,CAAC8D,OAAO;EAC5B,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,EAAE;EACX;EAEA,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,CAAC,EAAEC,CAAC,EAAEC,GAAG;EAEb,KAAKF,CAAC,IAAIF,OAAO,EAAE;IACjB,KAAKG,CAAC,IAAIH,OAAO,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvBE,GAAG,GAAGJ,OAAO,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,CAAC,CAAC;MACtB,IAAIC,GAAG,KAAK/B,EAAE,IAAKgC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAAIA,GAAG,CAACA,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC,KAAKlC,EAAG,EAAE;QACpE4B,OAAO,CAACpD,IAAI,CAACqD,CAAC,CAAC;MACjB;IACF;EACF;EAEA,IAAIhE,MAAM,CAAC1B,MAAM,EAAE;IACjByF,OAAO,GAAGA,OAAO,CAACO,MAAM,CAACT,UAAU,CAAC7D,MAAM,CAAC1B,MAAM,EAAE6D,EAAE,CAAC,CAAC;EACzD;EAEA,OAAO4B,OAAO;AAChB;AAEA,SAASvB,QAAQA,CAACxC,MAAM,EAAE6B,KAAK,EAAE;EAC/B,IAAIiC,OAAO,GAAG9D,MAAM,CAAC8D,OAAO;EAC5B,IAAI,CAACA,OAAO,EAAE;IACZ;EACF;EAEA,IAAIA,OAAO,CAACjC,KAAK,CAACM,EAAE,CAAC,IAAI,CAACnC,MAAM,CAAC1B,MAAM,EAAE;IACvC,IAAIoC,EAAE,GAAG,IAAI6D,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE1C,KAAK,CAACQ,SAAS,CAACC,EAAE,CAAC;IACzET,KAAK,CAACC,KAAK,GAAG,CAACgC,OAAO,CAACjC,KAAK,CAACM,EAAE,CAAC;IAChC2B,OAAO,CAACjC,KAAK,CAACM,EAAE,CAAC,GAAG,CAACzB,EAAE,EAAEmB,KAAK,CAAC2C,IAAI,CAAC;EACtC,CAAC,MAAM,IAAIxE,MAAM,CAAC1B,MAAM,EAAE;IACxBkE,QAAQ,CAACxC,MAAM,CAAC1B,MAAM,EAAEuD,KAAK,CAAC;EAChC;AACF;AAEA,SAASG,cAAcA,CAAChC,MAAM,EAAEmC,EAAE,EAAE;EAClC,IAAI2B,OAAO,GAAG9D,MAAM,CAAC8D,OAAO;EAC5B,IAAI,CAACA,OAAO,EAAE;IACZ;EACF;EAEA,IAAI,CAACA,OAAO,CAAC3B,EAAE,CAAC,IAAInC,MAAM,CAAC1B,MAAM,EAAE;IACjC,OAAO0D,cAAc,CAAChC,MAAM,CAAC1B,MAAM,EAAE6D,EAAE,CAAC;EAC1C;EAEA,IAAItB,aAAa,CAACsB,EAAE,CAAC,EAAE;IACrB;EACF;EACAtB,aAAa,CAACsB,EAAE,CAAC,GAAG,IAAI;EAExB,IAAIsC,MAAM,GAAGzE,MAAM,CAAC0E,KAAK,CAACvC,EAAE,CAAC;EAE7BrB,cAAc,CAACH,IAAI,CAAC,CAACX,MAAM,EAAEmC,EAAE,CAAC,CAAC;EAEjC,IAAIsC,MAAM,IAAIA,MAAM,CAACrE,GAAG,IAAIqE,MAAM,CAACrE,GAAG,CAACG,gBAAgB,CAAC8D,MAAM,EAAE;IAC9D,OAAO,IAAI;EACb;EAEA,OAAOR,UAAU,CAAC5B,MAAM,CAACC,aAAa,EAAEC,EAAE,CAAC,CAACwC,IAAI,CAAC,UAAUxC,EAAE,EAAE;IAC7D,OAAOH,cAAc,CAACC,MAAM,CAACC,aAAa,EAAEC,EAAE,CAAC;EACjD,CAAC,CAAC;AACJ;AAEA,SAASO,YAAYA,CAAC1C,MAAM,EAAEmC,EAAE,EAAE;EAChC,IAAIsC,MAAM,GAAGzE,MAAM,CAAC0E,KAAK,CAACvC,EAAE,CAAC;EAC7BnC,MAAM,CAACM,OAAO,GAAG,CAAC,CAAC;EACnB,IAAImE,MAAM,EAAE;IACVA,MAAM,CAACrE,GAAG,CAACC,IAAI,GAAGL,MAAM,CAACM,OAAO;EAClC;EAEA,IAAImE,MAAM,IAAIA,MAAM,CAACrE,GAAG,IAAIqE,MAAM,CAACrE,GAAG,CAACI,iBAAiB,CAAC6D,MAAM,EAAE;IAC/DI,MAAM,CAACrE,GAAG,CAACI,iBAAiB,CAACoB,OAAO,CAAC,UAAUgD,EAAE,EAAE;MACjDA,EAAE,CAAC5E,MAAM,CAACM,OAAO,CAAC;IACpB,CAAC,CAAC;EACJ;EAEA,OAAON,MAAM,CAAC0E,KAAK,CAACvC,EAAE,CAAC;EACvBnC,MAAM,CAACmC,EAAE,CAAC;EAEVsC,MAAM,GAAGzE,MAAM,CAAC0E,KAAK,CAACvC,EAAE,CAAC;EACzB,IAAIsC,MAAM,IAAIA,MAAM,CAACrE,GAAG,IAAIqE,MAAM,CAACrE,GAAG,CAACG,gBAAgB,CAAC8D,MAAM,EAAE;IAC9DI,MAAM,CAACrE,GAAG,CAACG,gBAAgB,CAACqB,OAAO,CAAC,UAAUgD,EAAE,EAAE;MAChDA,EAAE,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAO,IAAI;EACb;AACF", "file": "mysetprototypeof.2336be9c.js", "sourceRoot": "..", "sourcesContent": ["function _extendsv1(parent) {\n  let middle = Object.create(parent.prototype, {\n    count: {\n      writable: true,\n      value: 23,\n    },\n  }); // 中间对象\n  return middle;\n}\n\nfunction _extendsv2(parent) {\n  let middle = { count: 23 }; // 中间对象\n\n  //   setPrototypeOf 方法：修改一个对象的原型对象，这里是修改 middle 的原型对象为 parent.prototype\n  //   let newmiddle = Object.setPrototypeOf(middle, parent.prototype);\n  //   console.log(\"_extneds=>middle\", middle);\n  //   console.log(\"_extneds=>newmiddle\", newmiddle);\n  //   //   middle.__proto__ = parent.prototype; // 原型链继承;\n  //   return newmiddle;\n\n  // 简化版\n  return Object.setPrototypeOf(middle, parent.prototype);\n}\n\nlet middle = _extendsv2(People);\n\nfunction People(name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n}\n\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\n\nfunction ChinesePeople(name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n}\n\nChinesePeople.prototype.drink = function () {\n  console.log(this.name + \"喝水...\");\n};\n\nChinesePeople.prototype = middle;\nChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步\nlet chinesePeople1 = new ChinesePeople(\"张三\", \"男\", \"13888888888\", \"中文\");\nconsole.log(chinesePeople1);\nconsole.log(chinesePeople1 instanceof People); // true\nconsole.log(chinesePeople1 instanceof ChinesePeople); // true\n\n// TypeError: chinesePeople1.drink is not a function 错误\n// 原因：ChinesePeople.prototype = middle; 这行代码将 ChinesePeople 的原型指向了 middle，而 middle 并没有 drink 方法。\n// 这里setPrototypeOf方法修改了构造函数的原型对象，所以之前定义的方法被覆盖了，解决方法就是通过原型链的继承来解决，即chinesePeople1.__proto__还是指向ChinesePeople.prototype，ChinesePeople.prototype.__proto__指向People.prototype。\n// 这样chinesePeople1就可以访问自己原型对象空间中的方法，也可以访问父类原型对象空间中的方法。在mysetprototypeof2.js中已经解决了这个问题。\nchinesePeople1.drink();\n", "var OVERLAY_ID = '__parcel__error__overlay__';\n\nvar OldModule = module.bundle.Module;\n\nfunction Module(moduleName) {\n  OldModule.call(this, moduleName);\n  this.hot = {\n    data: module.bundle.hotData,\n    _acceptCallbacks: [],\n    _disposeCallbacks: [],\n    accept: function (fn) {\n      this._acceptCallbacks.push(fn || function () {});\n    },\n    dispose: function (fn) {\n      this._disposeCallbacks.push(fn);\n    }\n  };\n\n  module.bundle.hotData = null;\n}\n\nmodule.bundle.Module = Module;\nvar checkedAssets, assetsToAccept;\n\nvar parent = module.bundle.parent;\nif ((!parent || !parent.isParcelRequire) && typeof WebSocket !== 'undefined') {\n  var hostname = process.env.HMR_HOSTNAME || location.hostname;\n  var protocol = location.protocol === 'https:' ? 'wss' : 'ws';\n  var ws = new WebSocket(protocol + '://' + hostname + ':' + process.env.HMR_PORT + '/');\n  ws.onmessage = function(event) {\n    checkedAssets = {};\n    assetsToAccept = [];\n\n    var data = JSON.parse(event.data);\n\n    if (data.type === 'update') {\n      var handled = false;\n      data.assets.forEach(function(asset) {\n        if (!asset.isNew) {\n          var didAccept = hmrAcceptCheck(global.parcelRequire, asset.id);\n          if (didAccept) {\n            handled = true;\n          }\n        }\n      });\n\n      // Enable HMR for CSS by default.\n      handled = handled || data.assets.every(function(asset) {\n        return asset.type === 'css' && asset.generated.js;\n      });\n\n      if (handled) {\n        console.clear();\n\n        data.assets.forEach(function (asset) {\n          hmrApply(global.parcelRequire, asset);\n        });\n\n        assetsToAccept.forEach(function (v) {\n          hmrAcceptRun(v[0], v[1]);\n        });\n      } else if (location.reload) { // `location` global exists in a web worker context but lacks `.reload()` function.\n        location.reload();\n      }\n    }\n\n    if (data.type === 'reload') {\n      ws.close();\n      ws.onclose = function () {\n        location.reload();\n      }\n    }\n\n    if (data.type === 'error-resolved') {\n      console.log('[parcel] ✨ Error resolved');\n\n      removeErrorOverlay();\n    }\n\n    if (data.type === 'error') {\n      console.error('[parcel] 🚨  ' + data.error.message + '\\n' + data.error.stack);\n\n      removeErrorOverlay();\n\n      var overlay = createErrorOverlay(data);\n      document.body.appendChild(overlay);\n    }\n  };\n}\n\nfunction removeErrorOverlay() {\n  var overlay = document.getElementById(OVERLAY_ID);\n  if (overlay) {\n    overlay.remove();\n  }\n}\n\nfunction createErrorOverlay(data) {\n  var overlay = document.createElement('div');\n  overlay.id = OVERLAY_ID;\n\n  // html encode message and stack trace\n  var message = document.createElement('div');\n  var stackTrace = document.createElement('pre');\n  message.innerText = data.error.message;\n  stackTrace.innerText = data.error.stack;\n\n  overlay.innerHTML = (\n    '<div style=\"background: black; font-size: 16px; color: white; position: fixed; height: 100%; width: 100%; top: 0px; left: 0px; padding: 30px; opacity: 0.85; font-family: Menlo, Consolas, monospace; z-index: 9999;\">' +\n      '<span style=\"background: red; padding: 2px 4px; border-radius: 2px;\">ERROR</span>' +\n      '<span style=\"top: 2px; margin-left: 5px; position: relative;\">🚨</span>' +\n      '<div style=\"font-size: 18px; font-weight: bold; margin-top: 20px;\">' + message.innerHTML + '</div>' +\n      '<pre>' + stackTrace.innerHTML + '</pre>' +\n    '</div>'\n  );\n\n  return overlay;\n\n}\n\nfunction getParents(bundle, id) {\n  var modules = bundle.modules;\n  if (!modules) {\n    return [];\n  }\n\n  var parents = [];\n  var k, d, dep;\n\n  for (k in modules) {\n    for (d in modules[k][1]) {\n      dep = modules[k][1][d];\n      if (dep === id || (Array.isArray(dep) && dep[dep.length - 1] === id)) {\n        parents.push(k);\n      }\n    }\n  }\n\n  if (bundle.parent) {\n    parents = parents.concat(getParents(bundle.parent, id));\n  }\n\n  return parents;\n}\n\nfunction hmrApply(bundle, asset) {\n  var modules = bundle.modules;\n  if (!modules) {\n    return;\n  }\n\n  if (modules[asset.id] || !bundle.parent) {\n    var fn = new Function('require', 'module', 'exports', asset.generated.js);\n    asset.isNew = !modules[asset.id];\n    modules[asset.id] = [fn, asset.deps];\n  } else if (bundle.parent) {\n    hmrApply(bundle.parent, asset);\n  }\n}\n\nfunction hmrAcceptCheck(bundle, id) {\n  var modules = bundle.modules;\n  if (!modules) {\n    return;\n  }\n\n  if (!modules[id] && bundle.parent) {\n    return hmrAcceptCheck(bundle.parent, id);\n  }\n\n  if (checkedAssets[id]) {\n    return;\n  }\n  checkedAssets[id] = true;\n\n  var cached = bundle.cache[id];\n\n  assetsToAccept.push([bundle, id]);\n\n  if (cached && cached.hot && cached.hot._acceptCallbacks.length) {\n    return true;\n  }\n\n  return getParents(global.parcelRequire, id).some(function (id) {\n    return hmrAcceptCheck(global.parcelRequire, id)\n  });\n}\n\nfunction hmrAcceptRun(bundle, id) {\n  var cached = bundle.cache[id];\n  bundle.hotData = {};\n  if (cached) {\n    cached.hot.data = bundle.hotData;\n  }\n\n  if (cached && cached.hot && cached.hot._disposeCallbacks.length) {\n    cached.hot._disposeCallbacks.forEach(function (cb) {\n      cb(bundle.hotData);\n    });\n  }\n\n  delete bundle.cache[id];\n  bundle(id);\n\n  cached = bundle.cache[id];\n  if (cached && cached.hot && cached.hot._acceptCallbacks.length) {\n    cached.hot._acceptCallbacks.forEach(function (cb) {\n      cb();\n    });\n    return true;\n  }\n}\n"]}