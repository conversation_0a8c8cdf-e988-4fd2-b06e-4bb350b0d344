{"version": 3, "sources": ["src/ttt.js"], "names": ["Middle", "constructor", "Son", "Father", "prototype", "say", "console", "log", "son", "__proto__"], "mappings": "AAAA,SAASA,MAAMA,CAAA,EAAG;EAChB,IAAI,CAACC,WAAW,GAAGC,GAAG;AACxB;AAEA,SAASC,MAAMA,CAAA,EAAG,CAAC;AAEnBA,MAAM,CAACC,SAAS,CAACC,GAAG,GAAG,YAAY;EACjCC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;AAED,SAASL,GAAGA,CAAA,EAAG,CAAC;AAEhBF,MAAM,CAACI,SAAS,GAAGD,MAAM,CAACC,SAAS;AAEnCF,GAAG,CAACE,SAAS,GAAG,IAAIJ,MAAM,CAAC,CAAC;AAC5BM,OAAO,CAACC,GAAG,CAACL,GAAG,CAACE,SAAS,CAACH,WAAW,CAAC;AAEtC,IAAIO,GAAG,GAAG,IAAIN,GAAG,CAAC,CAAC;AACnBI,OAAO,CAACC,GAAG,CAACC,GAAG,CAACH,<PERSON>G,CAAC,CAAC,CAAC;AACtBC,OAAO,CAACC,GAAG,CAACC,GAAG,CAACC,SAAS,CAACR,WAAW,CAAC;AAEtCK,OAAO,CAACC,GAAG,CAACC,GAAG,CAAC", "file": "ttt.cd10e40f.js", "sourceRoot": "..", "sourcesContent": ["function Middle() {\n  this.constructor = Son;\n}\n\nfunction Father() {}\n\nFather.prototype.say = function () {\n  console.log(\"hello\");\n};\n\nfunction Son() {}\n\nMiddle.prototype = Father.prototype;\n\nSon.prototype = new Middle();\nconsole.log(Son.prototype.constructor);\n\nvar son = new Son();\nconsole.log(son.say());\nconsole.log(son.__proto__.constructor);\n\nconsole.log(son);\n"]}