{"version": 3, "sources": ["src/staticMemberextends.js"], "names": ["People", "name", "sex", "phone", "count", "commonDescribe", "console", "log", "prototype", "doEat", "people", "ChinesePeople", "language", "apply", "drink"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAATA,MAAMA,CAAaC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACvC;EACA,IAAI,CAACF,IAAI,GAAGA,IAAI,CAAC,CAAC;EAClB,IAAI,CAACC,GAAG,GAAGA,GAAG;EACd,IAAI,CAACC,KAAK,GAAGA,KAAK;AACpB,CAAC;AAEDH,MAAM,CAACI,KAAK,GAAG,GAAG,CAAC,CAAC;AACpBJ,MAAM,CAACK,cAAc,GAAG,YAAY;EAClCC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;AAEDP,MAAM,CAACQ,SAAS,CAACC,KAAK,GAAG,YAAY;EACnC;EACAH,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,IAAI,GAAG,OAAO,CAAC;AAClC,CAAC;AAED,IAAIS,MAAM,GAAG,IAAIV,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC;AAEjD,IAAIW,aAAa,GAAG,SAAhBA,aAAaA,CAAaV,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAES,QAAQ,EAAE;EACxD;EACAZ,MAAM,CAACa,KAAK,CAAC,IAAI,EAAE,CAACZ,IAAI,EAAEC,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EACxC,IAAI,CAACS,QAAQ,GAAGA,QAAQ;AAC1B,CAAC;AAEDD,aAAa,CAACH,SAAS,CAACM,KAAK,GAAG,YAAY;EAC1CR,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,IAAI,GAAG,OAAO,CAAC;AAClC,CAAC;AAEDK,OAAO,CAACC,GAAG,CAACG,MAAM,CAAC", "file": "staticMemberextends.31c4fdb1.js", "sourceRoot": "..", "sourcesContent": ["let People = function (name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n};\n\nPeople.count = 300; // 静态属性\nPeople.commonDescribe = function () {\n  console.log(\"这是一个人\");\n};\n\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\n\nlet people = new People(\"张三\", \"男\", \"13888888888\");\n\nlet ChinesePeople = function (name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n};\n\nChinesePeople.prototype.drink = function () {\n  console.log(this.name + \"喝水...\");\n};\n\nconsole.log(people);\n"]}