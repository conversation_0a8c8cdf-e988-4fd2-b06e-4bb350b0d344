"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bus = void 0;
var all_vehicle_1 = require("./all_vehicle");
// 子类 Bus 汽车类
var Bus = /** @class */ (function (_super) {
    __extends(Bus, _super);
    function Bus(brand_, vechileNo_, days_, deposit_, seatNum_) {
        var _this = _super.call(this, brand_, vechileNo_, days_, deposit_) || this;
        _this.seatNum = 0; // 座位数
        _this.seatNum = seatNum_;
        // 检查座位数是否合法
        if (_this.seatNum < 10 || _this.seatNum > 50 || _this.seatNum % 10 !== 0) {
            console.log("座位数不合法");
            throw new Error("座位数不合法，无法创建车辆");
        }
        return _this;
    }
    // 计算租金
    Bus.prototype.calcuateRent = function () {
        return this.days * this.getPriceBySeatNum();
    };
    // 获取价格
    Bus.prototype.getPriceBySeatNum = function () {
        var rentMap = {
            10: 100,
            20: 200,
            30: 300,
            40: 400,
            50: 500
        };
        var rentMoneyByDay = rentMap[this.seatNum];
        if (rentMoneyByDay === undefined) {
            console.log("无此座位数");
            throw new Error("无此座位数，无法计算租金");
        }
        console.log("日租金为：" + rentMoneyByDay + "元");
        return rentMoneyByDay;
    };
    return Bus;
}(all_vehicle_1.Vehicle));
exports.Bus = Bus;
var bus = new Bus("布加迪威龙", "京A12345", 3, 1000, 10);
var rent = bus.calcuateRent();
console.log("租金为：" + rent + "元");
