"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Vehicle = void 0;
// 父类 Vehicle 交通类
var Vehicle = /** @class */ (function () {
    function Vehicle(brand_, vechileNo_, days_, deposit_) {
        this.vechileNo = ''; // 车牌号
        this.days = 0; // 租赁天数
        this.total = 0; // 支付的租赁总费用
        this.deposit = 0; // 押金
        this.brand = brand_;
        this.vechileNo = vechileNo_;
        this.days = days_;
        this.deposit = deposit_;
    }
    Vehicle.prototype.getTotal = function () {
        return this.total;
    };
    // 计算租赁总费用
    Vehicle.prototype.calcuateRent = function () {
        console.log("".concat(this.brand, " ").concat(this.vechileNo, " \u5171").concat(this.days, "\u5929 \u88AB\u79DF\u7528"));
        return 0;
    };
    // 支付租金
    Vehicle.prototype.payDesposit = function () {
        console.log("".concat(this.brand, " ").concat(this.vechileNo, " \u652F\u4ED8").concat(this.deposit, "\u5143\u62BC\u91D1"));
    };
    // 安全检测方法
    Vehicle.prototype.showSafe = function () {
        console.log("".concat(this.brand, " ").concat(this.vechileNo, " \u5B89\u5168"));
    };
    return Vehicle;
}());
exports.Vehicle = Vehicle;
