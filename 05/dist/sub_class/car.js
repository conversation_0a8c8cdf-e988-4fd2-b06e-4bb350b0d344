"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var all_vehicle_1 = require("./all_vehicle");
var Car = /** @class */ (function (_super) {
    __extends(Car, _super);
    /**
     * 构造函数
     * @param brand_ 车辆品牌
     * @param vechileNo_ 车牌号
     * @param days_ 租赁天数
     * @param deposit_ 押金
     * @param type_ 车辆类型
     */
    function Car(brand_, vechileNo_, days_, deposit_, type_) {
        var _this = _super.call(this, brand_, vechileNo_, days_, deposit_) || this;
        console.log(days_);
        _this.type = type_;
        return _this;
    }
    /**
     * 计算租金
     *
     * 子类的重写的函数的访问修饰符范围不能比父类的小**** 重点
     */
    Car.prototype.calcuateRent = function () {
        _super.prototype.calcuateRent.call(this); // 调用父类方法 Vehicel.prototype.calcuateRent()
        return this.days * this.getPriceByType();
    };
    /**
     * 获取价格
     */
    Car.prototype.getPriceByType = function () {
        var rentMap = {
            "普拉多": 100,
            "玛莎拉蒂": 200,
            "兰博基尼": 300,
            "布加迪": 400,
            "布加迪威龙": 500
        };
        var rentMoneyByDay = rentMap[this.type];
        if (rentMoneyByDay === undefined) {
            console.log("无此车型");
            throw new Error("无此车型，无法计算租金");
        }
        console.log("日租金为：" + rentMoneyByDay + "元");
        return rentMoneyByDay;
    };
    return Car;
}(all_vehicle_1.Vehicle));
var car1 = new Car("布加迪威龙22", "京A12345", 3, 100, "布加迪威龙");
var rent = car1.calcuateRent();
console.log("支付的租赁总费用为：" + rent + "元");
