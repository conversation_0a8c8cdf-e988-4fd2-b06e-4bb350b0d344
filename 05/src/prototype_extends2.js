// Son.prototype = Parent.prototype的问题
function Parent(name, age) {
  this.name = name;
  this.age = age;
}

Parent.prototype.say = function () {
  console.log(this.name);
};

function Son(favor, sex) {
  this.favor = favor;
  this.sex = sex;
}

/**
 * Son.prototype = Parent.prototype;
 *这里将 Son.prototype 直接设置为 Parent.prototype，
 虽然让 Son 的实例能访问 Parent.prototype 上的方法，但并不会继承 Parent 实例的属性。
 parent1 实例的 name 属性是在 Parent 构造函数执行时添加到 parent1 对象自身的，而非 Parent.prototype 上。

属性查找机制
JavaScript 中，当访问对象的属性时，会先在对象自身查找该属性，若没找到，
就会沿着原型链在原型对象中查找。son1 自身没有 name 属性，其原型 Parent.prototype 上也没有 name 属性，
因此会返回 undefined。
 */
let parent1 = new Parent("小明", 12);
Son.prototype = Parent.prototype;
// 导致的问题：
// // 让Son.prototype  Parent.prototype son1.__proto__指向的原型对象空间【Parent.prototype】
Son.prototype.constructor = Son; // 让Son类的对象或函数原型.prototype指向的原型对象空间的constructor属性指向Son的构造函数空间

// 这里Parent的prototype指向了Son的prototype，这里显然还有问题
console.log(Parent.prototype.constructor === Son); // true
console.log(Son.prototype.constructor === Son); // true
console.log(Son.prototype.constructor === Parent); // false

let son1 = new Son("打篮球", "男");
console.log(son1.name); // 小明
