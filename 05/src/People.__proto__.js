// 构造函数
function People(name, gender, phone) {
  this.name = name;
  this.gender = gender;
  this.phone = phone;
}

// 在原型上添加方法
People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};

// 创建实例
let people = new People("张三", "男", "13888888888");

// 输出: People { name: '张三', gender: '男', phone: '13888888888' }
console.log(people);

// 输出: undefined (因为People不是一个函数，所以没有count属性)
console.log(People.count);

// 假设People.commonDescribe是一个在People原型上定义的方法
People.commonDescribe = function () {
  console.log("这是一个常见的描述");
};

// 输出: 这是一个常见的描述
People.commonDescribe();

// 调用原型上的方法
people.doEat(); // 输出: 张三吃饭...

// 输出: Function 的原型对象
console.log(People.__proto__ == Function.prototype);

// 输出: People 的原型对象，即 People.prototype
console.log(people.__proto__);
