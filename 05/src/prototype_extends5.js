function Parent(name, age) {
  this.name = name;
  this.age = age;
}

Parent.prototype.say = function () {
  console.log(this.name);
};

function Son(name, age, hobby) {
  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承
  Parent.call(this, name, age);
  this.hobby = hobby;
}

// 使用 Object.create 创建一个以 Parent.prototype 为原型的新对象
// 并将其赋值给 Son.prototype，实现方法继承
Son.prototype = Object.create(Parent.prototype);
// 修复 Son.prototype.constructor 指向问题
Son.prototype.constructor = Son;

// 为 Son 原型添加自己的方法
Son.prototype.showHobby = function () {
  console.log(this.hobby);
};

// 使用示例
const son = new Son("小明", 12, "打篮球");
son.say(); // 输出: 小明
son.showHobby(); // 输出: 打篮球
