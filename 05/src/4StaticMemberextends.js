function RootClass() {}

RootClass.rootname = "rootname___";

let People = function (name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
};

People.__proto__ = RootClass;

// TS类，双重性质 即是类型【new实例时TS类是类型】
// 当用TS类直接获取属性时就是对象

// JS函数 双重性质， 即是类型【new实例时JS函数也是类型】
// 当用JS函数直接获取属性时就是对象
// JS函数为对象时， 可以获取哪些属性呢？ 【包含 prototype, __proto__ 静态属性和静态方法]

People.count = 300; // 静态属性
People.commonDescribe = function () {
  console.log("这是一个人");
};

People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};

let people = new People("张三", "男", "13888888888");
console.log(people);
console.log(People.count);
People.commonDescribe();
people.doEat();

console.log(People.__proto__);
console.log(people.__proto__);

// people实例对象的__proto__指向People的prototype对象
// People的prototype的constructor指向People的构造函数
// People的count属性指向People的count静态属性
console.log(people.__proto__.constructor.count);

function ChinesePeople(name, sex, phone, language) {
  // 子类

  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;

  // People.call(this, name, sex, phone); // 借用父构造函数继承
  // this.language = language;
}

console.log("打印属性");
// ES6之前的实现

// 第一种方式：函数以对象形式呈现时，上面自有属性就是静态属性，上面自有方法就是静态方法
// for (let key in People) {
//   // 自有属性 还会查找__proto__指向的对象空间【这里是R sootClass】
//   console.log(key); // 打印静态属性和方法
// }

//
for (let key in People) {
  if (Object.hasOwnProperty.call(People, key)) {
    // 返回true,表示只返回本构造函数自身的自有属性（不包含__proto__指向的对象空间）
    console.log("key:", key); // 打印静态属性和方法
  }
}

// 第二种方式
// Object.keys(People).forEach((key) => {
//   // Object.keys()返回一个数组，包含对象自身的（不含继承的）所有的自有属性
//   console.log("key:", key); // 打印静态属性和方法
//   ChinesePeople[key] = People[key]; // ChinesePeople继承People的静态属性和方法
// });

// 第三种方式
ChinesePeople.__proto__ = People; // ChinesePeople继承People的静态属性和方法
console.log(ChinesePeople.count);

// es6的实现(效果等同于第三种方式)
Object.setPrototypeOf(ChinesePeople, People); // ChinesePeople继承People的静态属性和方法

let chinesePeople = new ChinesePeople("李四", "女", "13888888888", "中文");
console.log(chinesePeople);
console.log(ChinesePeople.count); // 这里是undefined

// 我们通过遍历People的自有属性，来继承People的静态属性和方法
for (let key in People) {
  if (Object.hasOwnProperty.call(People, key)) {
    // 返回true,表示只返回本构造函数自身的自有属性（不包含__proto__指向的对象空间）
    ChinesePeople[key] = People[key]; // ChinesePeople继承People的静态属性和方法
  }
}

console.log(ChinesePeople.count); // 通过遍历后，可以访问到People的静态属性和方法
