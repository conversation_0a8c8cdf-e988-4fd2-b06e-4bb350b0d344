function _extendsv1(parent) {
  let middle = Object.create(parent.prototype, {
    count: {
      writable: true,
      value: 23,
    },
  }); // 中间对象
  return middle;
}

function _extendsv2(parent) {
  let middle = { count: 23 }; // 中间对象

  //   setPrototypeOf 方法：修改一个对象的原型对象，这里是修改 middle 的原型对象为 parent.prototype
  //   let newmiddle = Object.setPrototypeOf(middle, parent.prototype);
  //   console.log("_extneds=>middle", middle);
  //   console.log("_extneds=>newmiddle", newmiddle);
  //   //   middle.__proto__ = parent.prototype; // 原型链继承;
  //   return newmiddle;

  // 简化版
  return Object.setPrototypeOf(middle, parent.prototype);
}

let middle = _extendsv2(People);

function People(name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
}

People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};

function ChinesePeople(name, sex, phone, language) {
  // 子类
  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;
}

ChinesePeople.prototype.drink = function () {
  console.log(this.name + "喝水...");
};

ChinesePeople.prototype = middle;
ChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步
let chinesePeople1 = new ChinesePeople("张三", "男", "13888888888", "中文");
console.log(chinesePeople1);
console.log(chinesePeople1 instanceof People); // true
console.log(chinesePeople1 instanceof ChinesePeople); // true

// TypeError: chinesePeople1.drink is not a function 错误
// 原因：ChinesePeople.prototype = middle; 这行代码将 ChinesePeople 的原型指向了 middle，而 middle 并没有 drink 方法。
// 这里setPrototypeOf方法修改了构造函数的原型对象，所以之前定义的方法被覆盖了，解决方法就是通过原型链的继承来解决，即chinesePeople1.__proto__还是指向ChinesePeople.prototype，ChinesePeople.prototype.__proto__指向People.prototype。
// 这样chinesePeople1就可以访问自己原型对象空间中的方法，也可以访问父类原型对象空间中的方法。在mysetprototypeof2.js中已经解决了这个问题。
chinesePeople1.drink();
