const extendsStatics = require("./6mytextendsStatics");

let __extends = (function () {
  return function (<PERSON>, Parent) {
    extendsStatics(<PERSON>, Parent);

    function Middle() {
      this.constructor = Son;
    }

    if (Parent) {
      Middle.prototype = Parent.prototype;
      Son.prototype = new Middle();
    } else {
      Son.prototype = Object.create(null);
    }
  };
})();

var Vehicle = /** @class */ (function () {
  function Vehicle(brand_, vechileNo_, days_, deposit_) {
    this.vechileNo = ""; // 车牌号
    this.days = 0; // 租赁天数
    this.total = 0; // 支付的租赁总费用
    this.deposit = 0; // 押金
    this.brand = brand_;
    this.vechileNo = vechileNo_;
    this.days = days_;
    this.deposit = deposit_;
  }
  Vehicle.prototype.getTotal = function () {
    return this.total;
  };

  Vehicle.count = 200000;

  // 计算租赁总费用
  Vehicle.prototype.calcuateRent = function () {
    console.log(
      ""
        .concat(this.brand, " ")
        .concat(this.vechileNo, " \u5171")
        .concat(this.days, "\u5929 \u88AB\u79DF\u7528")
    );
    return 0;
  };
  // 支付租金
  Vehicle.prototype.payDesposit = function () {
    console.log(
      ""
        .concat(this.brand, " ")
        .concat(this.vechileNo, " \u652F\u4ED8")
        .concat(this.deposit, "\u5143\u62BC\u91D1")
    );
  };
  // 安全检测方法
  Vehicle.prototype.showSafe = function () {
    console.log(
      "".concat(this.brand, " ").concat(this.vechileNo, " \u5B89\u5168")
    );
  };
  return Vehicle;
})();

var Car = (function (_super) {
  __extends(Car, _super);
  function Car(brand_, vechileNo_, days_, deposit_, type_) {
    var _this = _super.call(this, brand_, vechileNo_, days_, deposit_) || this;
    _this.type = type_;
    return _this;
  }

  Car.prototype.getPriceByType = function () {
    const rentMap = {
      普拉多: 100,
      玛莎拉蒂: 200,
      兰博基尼: 300,
      布加迪: 400,
      布加迪威龙: 500,
    };

    const rentMoneyByDay = rentMap[this.type];
    if (rentMoneyByDay === undefined) {
      console.log("无此车型");
      throw new Error("无此车型，无法计算租金");
    }

    console.log("日租金为：" + rentMoneyByDay + "元");
    return rentMoneyByDay;
  };

  Car.prototype.calcuateRent = function () {
    _super.prototype.calcuateRent.call(this);

    return this.days * this.getPriceByType();
  };
  return Car;
})(Vehicle);

var car = new Car("丰田", "京A12345", 10, 1000, "普拉多");
console.log(car.calcuateRent());

console.log(Car.count);
