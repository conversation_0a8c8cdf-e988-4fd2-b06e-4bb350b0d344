function Parent(name, age) {
  this.name = name;
  this.age = age;
  // 通过原型链继承+构造函数继承 构造函数执行了两次
  console.log("this name : ", this.name);
}

Parent.prototype.say = function () {
  console.log(this.name);
};

function Son(name, age, hobby) {
  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承
  Parent.call(this, name, age);
  this.hobby = hobby;
}

Son.prototype = new Parent();
// 修复 Son.prototype.constructor 指向问题
Son.prototype.constructor = Son;

// 为 Son 原型添加自己的方法
Son.prototype.showHobby = function () {
  console.log(this.hobby);
};

// 使用示例
const son = new Son("小明", 12, "打篮球");
son.say(); // 输出: 小明
son.showHobby(); // 输出: 打篮球

console.log(son);
