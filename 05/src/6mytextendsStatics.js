// 手写继承 静态属性和方法
// 之所以有这么多方法继承静态属性和方法，是因为要兼容es5和es6
module.exports = extendsStatics = (function () {
  function getStaticExtendsWithForIn(son, parent) {
    for (let key in parent) {
      if (Object.hasOwnProperty.call(parent, key)) {
        son[key] = parent[key];
      }
    }
  }

  function getStaticExtendsWithObjectKeys(son, parent) {
    Object.keys(parent).forEach((key) => {
      son[key] = parent[key];
    });
  }

  function getStaticExtendsWithObjectSetPrototypeOf(son, parent) {
    console.log("Object.setPrototypeOf", son);
    console.log("Object.setPrototypeOf", parent);
    Object.setPrototypeOf(son, parent);
  }

  function getStaticExtendsWithProto(son, parent) {
    son.__proto__ = parent;
  }

  return function (son, parent) {
    let MyextendsStatics =
      getStaticExtendsWithObjectSetPrototypeOf ||
      getStaticExtendsWithProto ||
      getStaticExtendsWithForIn ||
      getStaticExtendsWithObjectKeys;
    return MyextendsStatics(son, parent);
  };
})();
