let People = function (name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
};

People.count = 300; // 静态属性
People.commonDescribe = function () {
  console.log("这是一个人");
};

People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};

let people = new People("张三", "男", "13888888888");

let ChinesePeople = function (name, sex, phone, language) {
  // 子类
  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;
};

ChinesePeople.prototype.drink = function () {
  console.log(this.name + "喝水...");
};

console.log(people);
