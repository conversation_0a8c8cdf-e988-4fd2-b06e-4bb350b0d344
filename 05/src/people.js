// 原型链+借用构造函数继承（组合式继承）
function People(name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
}
People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};
function ChinesePeople(name, sex, phone, language) {
  // 子类
  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;
}

let chinesePeople = new ChinesePeople("张三", "男", "13888888888", "中文"); // 子类实例

console.log(chinesePeople);
console.log(chinesePeople instanceof People); // true
console.log(chinesePeople instanceof ChinesePeople); // true

console.log("chinesePeople无法访问父类原型对象的属性", chinesePeople.doEat);

// 使用原型链继承
ChinesePeople.prototype = new People(); // 原型链继承 （new People() 有很多缺点， Object.create(People.prototype) ）
ChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步

let chinesePeople1 = new ChinesePeople("张三", "男", "13888888888", "中文");
let chinesePeople2 = new ChinesePeople("李四", "女", "13999999999", "中文");

// 子类原型上的属性被共享
console.log(chinesePeople1.__proto__.name); // undefined（因为创建 People 实例时没传参数）
console.log(chinesePeople2.__proto__.name); // undefined
console.log(chinesePeople1.__proto__.doEat === chinesePeople2.__proto__.doEat); // true

console.log(chinesePeople1.hasOwnProperty("name")); // true
console.log(chinesePeople2.hasOwnProperty("name")); // true
