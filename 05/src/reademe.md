原型可以参考github
https://github.com/mqyqingfeng/blog/issues/2

这个很直观

# 原型链继承

## 原型链继承实现容易被遗忘的重要一步
Son.prototype.constructor = Son;

## 原型链继承常见疑问
Son.prototype = = Father.prototype 这样作为原型链继承的模式和Son.prototype = new Father() 有什么区别？
还是使用 Son.prototype = new Father()比较好一些

## 原型链继承的不足
局限性：不能通过子类构造函数向父类构造函数传递参数。

# 深度掌握TS继承准备：借用构造函数继承（冒充对象继承）
(1) 借用构造函数继承如何解决原型链继承的局限性
借用构造函数继承思想就是在子类【ChinesePeople】的内部借助apply()和call()方法调用
并传递参数给父类【People构造函数】

```javascript
fucntion People(name, sex, phone) { // People父构造函数【父类】
    this.name = name;  // 实例属性
    this.sex = sex;
    this.phone = phone;
}

People.prototype.doEat = function () { // 原型方法
    console.log(this.name + '吃饭...');
}

function ChinesePeople(name, sex, phone, language) { // 子类
    People.apply(this, [name, sex, phone]); // 借用父构造函数继承
    this.language = language;
}

let chinesePeople = new ChinesePeople('张三', '男', '13888888888', '中文');
```

(2) 借用构造函数继承的不足
不能继承父类原型上的属性和方法。

## 原型链继承和借用构造函数继承的结合
### (1)借用构造函数+原型链继承组合模式的优势
优势1:具备借用构造函数的优点:子类【chinesePeople 构造函数】的内部可以向父类【 People 构造函数】 传递参数
优势2:具备原型链继承的优点:ChinesePeople.prototype 和 new ChinesePeople()出来的实例对象变量和实例都可以访问父类【 People 构造函数】 原型对象上的属性和方法。
```javascript
function People(name, sex, phone) { // People父构造函数【父类】
    this.name = name;  // 实例属性
    this.sex = sex;
    this.phone = phone; 
}
People.prototype.doEat = function () { // 原型方法
    console.log(this.name + '吃饭...');
}   
function ChinesePeople(name, sex, phone, language) { // 子类
    People.apply(this, [name, sex, phone]); // 借用父构造函数继承
    this.language = language;
}
ChinesePeople.prototype = new People(); // 原型链继承 （new People() 有很多缺点， Object.create(People.prototype) ）
ChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步
let chinesePeople = new ChinesePeople('张三', '男', '13888888888', '中文'); // 子类实例

console.log(chinesePeople);
console.log(chinesePeople instanceof People); // true
console.log(chinesePeople instanceof ChinesePeople); // true
```
### （2）借用构造函数+原型链继承组合模式的不足:
缺点:调用了两次父类构造函数 【 People 构造函数】 new People 调用构造函数带来问题:
1.进入 People 构造函数为属性赋值，分配内存空间，浪费内存;
2.赋值导致效率下降一些，关键是new People 赋的值无意义，出现代码冗余，new ChinesePeople出来的对象和这些值毫不相干，是通过子类 ChinesePeople 构造函数中的 apply 来向父类People构造函数赋值.

## 寄生组合继承
```javascript
function People(name, sex, phone) { // People父构造函数【父类】
    this.name = name;  // 实例属性
    this.sex = sex;
    this.phone = phone;
}
People.prototype.doEat = function () { // 原型方法
    console.log(this.name + '吃饭...');
}
function ChinesePeople(name, sex, phone, language) { // 子类
    People.apply(this, [name, sex, phone]); // 借用父构造函数继承
    this.language = language;
}
// ChinesePeople.prototype = Object.create(People.prototype); // 原型链继承 （new People() 有很多缺点， Object.create(People.prototype) ）

// 寄生组合继承
function midle() {

}
midle.prototype = People.prototype;  // 约等同于  Object.create(People.prototype)
ChinesePeople.prototype =  new midle();

ChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步
let chinesePeople = new ChinesePeople('张三', '男', '13888888888', '中文'); // 子类实例
console.log(chinesePeople);
console.log(chinesePeople instanceof People); // true
console.log(chinesePeople instanceof ChinesePeople); // true
```

