// 寄生组合继承(最佳实践--最常用的继承方式)
function Parent(name, age) {
  this.name = name;
  this.age = age;
  // 通过原型链继承+构造函数继承 构造函数执行了两次
  console.log("this name : ", this.name);
}

Parent.prototype.say = function () {
  console.log(this.name);
};

function Son(name, age, hobby) {
  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承
  Parent.call(this, name, age);
  this.hobby = hobby;
}

function middle() {
  this.count = 22;
}
middle.prototype = Parent.prototype;

Son.prototype = new middle();

// 为 Son 原型添加自己的方法
Son.prototype.showHobby = function () {
  console.log(this.hobby);
};

// 使用示例
const son = new Son("小明", 12, "打篮球");
son.say(); // 输出: 小明
son.showHobby(); // 输出: 打篮球

// 此时Son.prototype.constructor指向的是Parent的构造函数空间
console.log(Son.prototype.constructor);

// 修复 Son.prototype.constructor 指向问题
Son.prototype.constructor = Son;
const son2 = new Son("小明", 12, "打篮球");

console.log(Son.prototype.constructor);
console.log("-----", son2);
