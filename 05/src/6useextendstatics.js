const extendsStatics = require("./6mytextendsStatics");

function People(name, age) {
  this.name = name;
  this.age = age;
}

People.prototype.say = function () {
  console.log("hello");
};

People.count = 200;
People.commonDescribe = function () {
  console.log("这是一个人");
};

function ChinesePeople(name, age, language) {
  People.apply(this, [name, age]);
  this.language = language;
}

extendsStatics(ChinesePeople, People);

console.log(ChinesePeople.count);
