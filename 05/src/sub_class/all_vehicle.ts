// 父类 Vehicle 交通类
export class Vehicle {
  public brand: string; // 品牌
  public vechileNo: string = ''; // 车牌号
  public days: number = 0; // 租赁天数
  public total:number = 0 ;// 支付的租赁总费用
  public deposit: number = 0; // 押金

 constructor( brand_: string, vechileNo_: string, days_: number, deposit_: number) {
    this.brand = brand_;
    this.vechileNo = vechileNo_;
    this.days = days_;
    this.deposit = deposit_;
  }
  public getTotal(): number {
    return this.total;
  }

  // 计算租赁总费用
  protected calcuateRent():number {
    console.log(`${this.brand} ${this.vechileNo} 共${this.days}天 被租用`);
    return 0;
  }

  // 支付租金
  public payDesposit():void {
    console.log(`${this.brand} ${this.vechileNo} 支付${this.deposit}元押金`);
  }

  // 安全检测方法
  public showSafe() {
    console.log(`${this.brand} ${this.vechileNo} 安全`);
  }
}

