import { Vehicle } from "./all_vehicle";

class Car extends Vehicle {
 
  public type: string; // 车辆类型

  /**
   * 构造函数
   * @param brand_ 车辆品牌
   * @param vechileNo_ 车牌号
   * @param days_ 租赁天数
   * @param deposit_ 押金
   * @param type_ 车辆类型
   */
  constructor(
    brand_: string,
    vechileNo_: string,
    days_: number,
    deposit_: number,
    type_: string
  ) {
    super(brand_, vechileNo_, days_, deposit_);
    console.log(days_);
    this.type = type_;
  }

  /**
   * 计算租金
   * 
   * 子类的重写的函数的访问修饰符范围不能比父类的小**** 重点
   */
  public calcuateRent(): number { // 方法重写（override）
    super.calcuateRent(); // 调用父类方法 Vehicel.prototype.calcuateRent()
    return this.days * this.getPriceByType();
  }

  /**
   * 获取价格 
   */
  public getPriceByType(): number {
    const rentMap: Record<string, number> = {
      "普拉多": 100,
      "玛莎拉蒂": 200,
      "兰博基尼": 300,
      "布加迪": 400,
      "布加迪威龙": 500
    };

    const rentMoneyByDay = rentMap[this.type];
    if (rentMoneyByDay === undefined) {
      console.log("无此车型");
      throw new Error("无此车型，无法计算租金");
    }

    console.log("日租金为：" + rentMoneyByDay + "元");
    return rentMoneyByDay;
  }
}

const car1 = new Car("布加迪威龙22", "京A12345", 3, 100, "布加迪威龙");
const rent = car1.calcuateRent();
console.log("支付的租赁总费用为：" + rent + "元");