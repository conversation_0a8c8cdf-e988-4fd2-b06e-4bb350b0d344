import { Vehicle } from './all_vehicle';

// 子类 Bus 汽车类
export class Bus extends Vehicle {
  public seatNum: number = 0; // 座位数

  constructor(brand_: string, vechileNo_: string, days_: number, deposit_: number, seatNum_: number) {
    super(brand_, vechileNo_, days_, deposit_);
    this.seatNum = seatNum_;

    // 检查座位数是否合法
    if (this.seatNum < 10 || this.seatNum > 50 || this.seatNum % 10 !== 0) {
      console.log("座位数不合法");
      throw new Error("座位数不合法，无法创建车辆");
    }
  }
  // 计算租金
  public calcuateRent(): number {
    return this.days * this.getPriceBySeatNum();
  }

  // 获取价格
  public getPriceBySeatNum(): number {
    const rentMap: Record<number, number> = {
      10: 100,
      20: 200,
      30: 300,
      40: 400,
      50: 500
    };
    const rentMoneyByDay = rentMap[this.seatNum];
    if (rentMoneyByDay === undefined) {
      console.log("无此座位数");
      throw new Error("无此座位数，无法计算租金");
    }
    console.log("日租金为：" + rentMoneyByDay + "元");
    return rentMoneyByDay;
  }
}

const bus = new Bus("布加迪威龙", "京A12345", 3, 1000, 10);
const rent = bus.calcuateRent();
console.log("租金为：" + rent + "元");



