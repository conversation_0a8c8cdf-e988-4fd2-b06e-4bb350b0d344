function People(name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
  this.say = function () {
    console.log("say...");
  };
}
People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};
function ChinesePeople(name, sex, phone, language) {
  // 子类
  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;
}
// ChinesePeople.prototype = Object.create(People.prototype); // 原型链继承 （new People() 有很多缺点， Object.create(People.prototype) ）

// 构建一个公用的寄生组合继承函数【最佳原型继承】- 即是Object.creae()的实现
function createNewPrototypeObj(root, son) {
  function Middle() {
    this.count = 23;
    // this.constructor = son; // 等同 ChinesePeople.prototype.constructor = ChinesePeople
  }

  Middle.prototype = root.prototype; // People.prototype;

  let middle = new Middle();
  return middle;
}

// 寄生组合继承
// function midle() {}
// midle.prototype = People.prototype; // 约等同于  Object.create(People.prototype)
// ChinesePeople.prototype = new midle();

ChinesePeople.prototype = createNewPrototypeObj(People, ChinesePeople);

ChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步
let chinesePeople = new ChinesePeople("张三", "男", "13888888888", "中文"); // 子类实例
console.log(chinesePeople);
chinesePeople.doEat();
