function Parent(name, age) {
  this.name = name;
  this.age = age;
}

Parent.prototype.say = function () {
  console.log(this.name);
};

function Son(favor, sex) {
  this.favor = favor;
  this.sex = sex;
}

// 方式一：
let parent = new Parent("小明", 12);
let son = new Son("打篮球", "男");
// 原型继承
son.__proto__ = parent;
console.log(son.name); // 小明
console.log(Son.prototype.constructor === Son); // true

// 方式二：
let parent1 = new Parent("小明", 12);
Son.prototype = parent1;
Son.prototype.constructor = Son; // 让Son类的对象或函数原型.prototype指向的原型对象空间的constructor属性指向Son的构造函数空间
let son1 = new Son("打篮球", "男");
console.log(son1.name); // 小明
console.log(Son.prototype.constructor === true); // true
