function Father() {
  this.fatherProperty = "father value";
}

Father.prototype.sayHello = function () {
  console.log("Hello from Father");
};

function Son() {
  this.sonProperty = "son value";
}

Son.prototype = new Father();
Son.prototype.constructor = Son;

const son = new Son();
const father = new Father();

// 修改 Son 原型不会影响 Father 原型
Son.prototype.sayHi = function () {
  console.log("Hi from Son");
};

father.sayHi(); // 报错: father.sayHi is not a function
