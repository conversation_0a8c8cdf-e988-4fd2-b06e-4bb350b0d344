function _extendsv3(son, parent) {
  // son.prototype.__proto__ = parent.prototype;
  return Object.setPrototypeOf(son.prototype, parent.prototype);
}

function People(name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
}

People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};

function ChinesePeople(name, sex, phone, language) {
  // 子类
  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;
}

ChinesePeople.prototype.drink = function () {
  console.log(this.name + "喝水...");
};

_extendsv3(ChinesePeople, People);

let chinesePeople1 = new ChinesePeople("张三", "男", "13888888888", "中文");

console.log(
  "chinesep1实例对象的原型：",
  chinesePeople1.__proto__ == ChinesePeople.prototype
); // true
console.log(
  "chinesep1实例对象的原型的原型：",
  chinesePeople1.__proto__.__proto__ == People.prototype
);
chinesePeople1.__proto__.__proto__ == People.prototype; // true

console.log(ChinesePeople.prototype.constructor);
console.log(chinesePeople1);
console.log(chinesePeople1 instanceof People); // true
console.log(chinesePeople1 instanceof ChinesePeople); // true

chinesePeople1.drink();
