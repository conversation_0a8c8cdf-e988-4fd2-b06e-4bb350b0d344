{"id": "src/prototype_extends3.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "function Parent(name, age) {\n  this.name = name;\n  this.age = age;\n  // 通过原型链继承+构造函数继承 构造函数执行了两次\n  console.log(\"this name : \", this.name);\n}\nParent.prototype.say = function () {\n  console.log(this.name);\n};\nfunction Son(name, age, hobby) {\n  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承\n  Parent.call(this, name, age);\n  this.hobby = hobby;\n}\nSon.prototype = new Parent();\n// 修复 Son.prototype.constructor 指向问题\nSon.prototype.constructor = Son;\n\n// 为 Son 原型添加自己的方法\nSon.prototype.showHobby = function () {\n  console.log(this.hobby);\n};\n\n// 使用示例\nvar son = new Son(\"小明\", 12, \"打篮球\");\nson.say(); // 输出: 小明\nson.showHobby(); // 输出: 打篮球\n\nconsole.log(son);"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 1, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 9}, "name": "Parent"}, {"generated": {"line": 1, "column": 15}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 15}, "name": "Parent"}, {"generated": {"line": 1, "column": 16}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 16}, "name": "name"}, {"generated": {"line": 1, "column": 20}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 20}}, {"generated": {"line": 1, "column": 22}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 22}, "name": "age"}, {"generated": {"line": 1, "column": 25}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 25}}, {"generated": {"line": 1, "column": 27}, "source": "src/prototype_extends3.js", "original": {"line": 1, "column": 27}}, {"generated": {"line": 2, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 2, "column": 2}}, {"generated": {"line": 2, "column": 6}, "source": "src/prototype_extends3.js", "original": {"line": 2, "column": 6}}, {"generated": {"line": 2, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 2, "column": 7}, "name": "name"}, {"generated": {"line": 2, "column": 11}, "source": "src/prototype_extends3.js", "original": {"line": 2, "column": 11}}, {"generated": {"line": 2, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 2, "column": 14}, "name": "name"}, {"generated": {"line": 2, "column": 18}, "source": "src/prototype_extends3.js", "original": {"line": 2, "column": 18}}, {"generated": {"line": 3, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 3, "column": 2}}, {"generated": {"line": 3, "column": 6}, "source": "src/prototype_extends3.js", "original": {"line": 3, "column": 6}}, {"generated": {"line": 3, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 3, "column": 7}, "name": "age"}, {"generated": {"line": 3, "column": 10}, "source": "src/prototype_extends3.js", "original": {"line": 3, "column": 10}}, {"generated": {"line": 3, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 3, "column": 13}, "name": "age"}, {"generated": {"line": 3, "column": 16}, "source": "src/prototype_extends3.js", "original": {"line": 3, "column": 16}}, {"generated": {"line": 4, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 4, "column": 2}}, {"generated": {"line": 5, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 2}, "name": "console"}, {"generated": {"line": 5, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 9}}, {"generated": {"line": 5, "column": 10}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 10}, "name": "log"}, {"generated": {"line": 5, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 13}}, {"generated": {"line": 5, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 14}}, {"generated": {"line": 5, "column": 28}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 28}}, {"generated": {"line": 5, "column": 30}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 30}}, {"generated": {"line": 5, "column": 34}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 34}}, {"generated": {"line": 5, "column": 35}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 35}, "name": "name"}, {"generated": {"line": 5, "column": 39}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 39}}, {"generated": {"line": 5, "column": 40}, "source": "src/prototype_extends3.js", "original": {"line": 5, "column": 40}}, {"generated": {"line": 6, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 6, "column": 0}}, {"generated": {"line": 7, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 0}, "name": "Parent"}, {"generated": {"line": 7, "column": 6}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 6}}, {"generated": {"line": 7, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 7}, "name": "prototype"}, {"generated": {"line": 7, "column": 16}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 16}}, {"generated": {"line": 7, "column": 17}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 17}, "name": "say"}, {"generated": {"line": 7, "column": 20}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 20}}, {"generated": {"line": 7, "column": 23}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 23}}, {"generated": {"line": 7, "column": 35}, "source": "src/prototype_extends3.js", "original": {"line": 8, "column": 35}}, {"generated": {"line": 8, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 2}, "name": "console"}, {"generated": {"line": 8, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 9}}, {"generated": {"line": 8, "column": 10}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 10}, "name": "log"}, {"generated": {"line": 8, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 13}}, {"generated": {"line": 8, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 14}}, {"generated": {"line": 8, "column": 18}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 18}}, {"generated": {"line": 8, "column": 19}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 19}, "name": "name"}, {"generated": {"line": 8, "column": 23}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 23}}, {"generated": {"line": 8, "column": 24}, "source": "src/prototype_extends3.js", "original": {"line": 9, "column": 24}}, {"generated": {"line": 9, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 10, "column": 0}}, {"generated": {"line": 9, "column": 1}, "source": "src/prototype_extends3.js", "original": {"line": 10, "column": 1}}, {"generated": {"line": 10, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 0}}, {"generated": {"line": 10, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 9}, "name": "Son"}, {"generated": {"line": 10, "column": 12}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 12}, "name": "Son"}, {"generated": {"line": 10, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 13}, "name": "name"}, {"generated": {"line": 10, "column": 17}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 17}}, {"generated": {"line": 10, "column": 19}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 19}, "name": "age"}, {"generated": {"line": 10, "column": 22}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 22}}, {"generated": {"line": 10, "column": 24}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 24}, "name": "hobby"}, {"generated": {"line": 10, "column": 29}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 29}}, {"generated": {"line": 10, "column": 31}, "source": "src/prototype_extends3.js", "original": {"line": 12, "column": 31}}, {"generated": {"line": 11, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 13, "column": 2}}, {"generated": {"line": 12, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 2}, "name": "Parent"}, {"generated": {"line": 12, "column": 8}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 8}}, {"generated": {"line": 12, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 9}, "name": "call"}, {"generated": {"line": 12, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 13}}, {"generated": {"line": 12, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 14}}, {"generated": {"line": 12, "column": 18}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 18}}, {"generated": {"line": 12, "column": 20}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 20}, "name": "name"}, {"generated": {"line": 12, "column": 24}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 24}}, {"generated": {"line": 12, "column": 26}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 26}, "name": "age"}, {"generated": {"line": 12, "column": 29}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 29}}, {"generated": {"line": 12, "column": 30}, "source": "src/prototype_extends3.js", "original": {"line": 14, "column": 30}}, {"generated": {"line": 13, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 15, "column": 2}}, {"generated": {"line": 13, "column": 6}, "source": "src/prototype_extends3.js", "original": {"line": 15, "column": 6}}, {"generated": {"line": 13, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 15, "column": 7}, "name": "hobby"}, {"generated": {"line": 13, "column": 12}, "source": "src/prototype_extends3.js", "original": {"line": 15, "column": 12}}, {"generated": {"line": 13, "column": 15}, "source": "src/prototype_extends3.js", "original": {"line": 15, "column": 15}, "name": "hobby"}, {"generated": {"line": 13, "column": 20}, "source": "src/prototype_extends3.js", "original": {"line": 15, "column": 20}}, {"generated": {"line": 14, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 16, "column": 0}}, {"generated": {"line": 15, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 0}, "name": "Son"}, {"generated": {"line": 15, "column": 3}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 3}}, {"generated": {"line": 15, "column": 4}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 4}, "name": "prototype"}, {"generated": {"line": 15, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 13}}, {"generated": {"line": 15, "column": 16}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 16}}, {"generated": {"line": 15, "column": 20}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 20}, "name": "Parent"}, {"generated": {"line": 15, "column": 26}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 26}}, {"generated": {"line": 15, "column": 27}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 27}}, {"generated": {"line": 15, "column": 28}, "source": "src/prototype_extends3.js", "original": {"line": 18, "column": 28}}, {"generated": {"line": 16, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 19, "column": 0}}, {"generated": {"line": 17, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 0}, "name": "Son"}, {"generated": {"line": 17, "column": 3}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 3}}, {"generated": {"line": 17, "column": 4}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 4}, "name": "prototype"}, {"generated": {"line": 17, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 13}}, {"generated": {"line": 17, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 14}, "name": "constructor"}, {"generated": {"line": 17, "column": 25}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 25}}, {"generated": {"line": 17, "column": 28}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 28}, "name": "Son"}, {"generated": {"line": 17, "column": 31}, "source": "src/prototype_extends3.js", "original": {"line": 20, "column": 31}}, {"generated": {"line": 19, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 22, "column": 0}}, {"generated": {"line": 20, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 0}, "name": "Son"}, {"generated": {"line": 20, "column": 3}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 3}}, {"generated": {"line": 20, "column": 4}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 4}, "name": "prototype"}, {"generated": {"line": 20, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 13}}, {"generated": {"line": 20, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 14}, "name": "showH<PERSON><PERSON>"}, {"generated": {"line": 20, "column": 23}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 23}}, {"generated": {"line": 20, "column": 26}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 26}}, {"generated": {"line": 20, "column": 38}, "source": "src/prototype_extends3.js", "original": {"line": 23, "column": 38}}, {"generated": {"line": 21, "column": 2}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 2}, "name": "console"}, {"generated": {"line": 21, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 9}}, {"generated": {"line": 21, "column": 10}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 10}, "name": "log"}, {"generated": {"line": 21, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 13}}, {"generated": {"line": 21, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 14}}, {"generated": {"line": 21, "column": 18}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 18}}, {"generated": {"line": 21, "column": 19}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 19}, "name": "hobby"}, {"generated": {"line": 21, "column": 24}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 24}}, {"generated": {"line": 21, "column": 25}, "source": "src/prototype_extends3.js", "original": {"line": 24, "column": 25}}, {"generated": {"line": 22, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 25, "column": 0}}, {"generated": {"line": 22, "column": 1}, "source": "src/prototype_extends3.js", "original": {"line": 25, "column": 1}}, {"generated": {"line": 24, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 27, "column": 0}}, {"generated": {"line": 25, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 0}}, {"generated": {"line": 25, "column": 4}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 6}, "name": "son"}, {"generated": {"line": 25, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 9}}, {"generated": {"line": 25, "column": 10}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 12}}, {"generated": {"line": 25, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 16}, "name": "Son"}, {"generated": {"line": 25, "column": 17}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 19}}, {"generated": {"line": 25, "column": 18}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 20}}, {"generated": {"line": 25, "column": 22}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 24}}, {"generated": {"line": 25, "column": 24}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 26}}, {"generated": {"line": 25, "column": 26}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 28}}, {"generated": {"line": 25, "column": 28}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 30}}, {"generated": {"line": 25, "column": 33}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 35}}, {"generated": {"line": 25, "column": 34}, "source": "src/prototype_extends3.js", "original": {"line": 28, "column": 36}}, {"generated": {"line": 26, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 0}, "name": "son"}, {"generated": {"line": 26, "column": 3}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 3}}, {"generated": {"line": 26, "column": 4}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 4}, "name": "say"}, {"generated": {"line": 26, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 7}}, {"generated": {"line": 26, "column": 8}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 8}}, {"generated": {"line": 26, "column": 9}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 9}}, {"generated": {"line": 26, "column": 10}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 10}}, {"generated": {"line": 26, "column": 11}, "source": "src/prototype_extends3.js", "original": {"line": 29, "column": 11}}, {"generated": {"line": 27, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 0}, "name": "son"}, {"generated": {"line": 27, "column": 3}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 3}}, {"generated": {"line": 27, "column": 4}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 4}, "name": "showH<PERSON><PERSON>"}, {"generated": {"line": 27, "column": 13}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 13}}, {"generated": {"line": 27, "column": 14}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 14}}, {"generated": {"line": 27, "column": 15}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 15}}, {"generated": {"line": 27, "column": 16}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 16}}, {"generated": {"line": 27, "column": 17}, "source": "src/prototype_extends3.js", "original": {"line": 30, "column": 17}}, {"generated": {"line": 29, "column": 0}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 0}, "name": "console"}, {"generated": {"line": 29, "column": 7}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 7}}, {"generated": {"line": 29, "column": 8}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 8}, "name": "log"}, {"generated": {"line": 29, "column": 11}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 11}}, {"generated": {"line": 29, "column": 12}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 12}, "name": "son"}, {"generated": {"line": 29, "column": 15}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 15}}, {"generated": {"line": 29, "column": 16}, "source": "src/prototype_extends3.js", "original": {"line": 32, "column": 16}}], "sources": {"src/prototype_extends3.js": "function Parent(name, age) {\n  this.name = name;\n  this.age = age;\n  // 通过原型链继承+构造函数继承 构造函数执行了两次\n  console.log(\"this name : \", this.name);\n}\n\nParent.prototype.say = function () {\n  console.log(this.name);\n};\n\nfunction Son(name, age, hobby) {\n  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承\n  Parent.call(this, name, age);\n  this.hobby = hobby;\n}\n\nSon.prototype = new Parent();\n// 修复 Son.prototype.constructor 指向问题\nSon.prototype.constructor = Son;\n\n// 为 Son 原型添加自己的方法\nSon.prototype.showHobby = function () {\n  console.log(this.hobby);\n};\n\n// 使用示例\nconst son = new Son(\"小明\", 12, \"打篮球\");\nson.say(); // 输出: 小明\nson.showHobby(); // 输出: 打篮球\n\nconsole.log(son);\n"}, "lineCount": null}}, "error": null, "hash": "de404e1e31923c4881ed6b6e83a3eecc", "cacheData": {"env": {}}}