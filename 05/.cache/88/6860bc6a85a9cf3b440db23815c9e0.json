{"id": "src/staticMemberextends.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "var People = function People(name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n};\nPeople.count = 300; // 静态属性\nPeople.commonDescribe = function () {\n  console.log(\"这是一个人\");\n};\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\nvar people = new People(\"张三\", \"男\", \"13888888888\");\nvar ChinesePeople = function ChinesePeople(name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n};\nChinesePeople.prototype.drink = function () {\n  console.log(this.name + \"喝水...\");\n};\nconsole.log(people);"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 1, "column": 4}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 4}, "name": "People"}, {"generated": {"line": 1, "column": 10}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 10}}, {"generated": {"line": 1, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 13}}, {"generated": {"line": 1, "column": 22}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 4}, "name": "People"}, {"generated": {"line": 1, "column": 28}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 10}, "name": "People"}, {"generated": {"line": 1, "column": 29}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 23}, "name": "name"}, {"generated": {"line": 1, "column": 33}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 27}}, {"generated": {"line": 1, "column": 35}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 29}, "name": "sex"}, {"generated": {"line": 1, "column": 38}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 32}}, {"generated": {"line": 1, "column": 40}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 34}, "name": "phone"}, {"generated": {"line": 1, "column": 45}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 39}}, {"generated": {"line": 1, "column": 47}, "source": "src/staticMemberextends.js", "original": {"line": 1, "column": 41}}, {"generated": {"line": 2, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 2, "column": 2}}, {"generated": {"line": 3, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 2}}, {"generated": {"line": 3, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 6}}, {"generated": {"line": 3, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 7}, "name": "name"}, {"generated": {"line": 3, "column": 11}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 11}}, {"generated": {"line": 3, "column": 14}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 14}, "name": "name"}, {"generated": {"line": 3, "column": 18}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 18}}, {"generated": {"line": 3, "column": 19}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 19}}, {"generated": {"line": 3, "column": 20}, "source": "src/staticMemberextends.js", "original": {"line": 3, "column": 20}}, {"generated": {"line": 4, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 4, "column": 2}}, {"generated": {"line": 4, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 4, "column": 6}}, {"generated": {"line": 4, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 4, "column": 7}, "name": "sex"}, {"generated": {"line": 4, "column": 10}, "source": "src/staticMemberextends.js", "original": {"line": 4, "column": 10}}, {"generated": {"line": 4, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 4, "column": 13}, "name": "sex"}, {"generated": {"line": 4, "column": 16}, "source": "src/staticMemberextends.js", "original": {"line": 4, "column": 16}}, {"generated": {"line": 5, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 5, "column": 2}}, {"generated": {"line": 5, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 5, "column": 6}}, {"generated": {"line": 5, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 5, "column": 7}, "name": "phone"}, {"generated": {"line": 5, "column": 12}, "source": "src/staticMemberextends.js", "original": {"line": 5, "column": 12}}, {"generated": {"line": 5, "column": 15}, "source": "src/staticMemberextends.js", "original": {"line": 5, "column": 15}, "name": "phone"}, {"generated": {"line": 5, "column": 20}, "source": "src/staticMemberextends.js", "original": {"line": 5, "column": 20}}, {"generated": {"line": 6, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 6, "column": 0}}, {"generated": {"line": 6, "column": 1}, "source": "src/staticMemberextends.js", "original": {"line": 6, "column": 1}}, {"generated": {"line": 7, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 0}, "name": "People"}, {"generated": {"line": 7, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 6}}, {"generated": {"line": 7, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 7}, "name": "count"}, {"generated": {"line": 7, "column": 12}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 12}}, {"generated": {"line": 7, "column": 15}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 15}}, {"generated": {"line": 7, "column": 18}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 18}}, {"generated": {"line": 7, "column": 19}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 19}}, {"generated": {"line": 7, "column": 20}, "source": "src/staticMemberextends.js", "original": {"line": 8, "column": 20}}, {"generated": {"line": 8, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 9, "column": 0}, "name": "People"}, {"generated": {"line": 8, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 9, "column": 6}}, {"generated": {"line": 8, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 9, "column": 7}, "name": "commonDescribe"}, {"generated": {"line": 8, "column": 21}, "source": "src/staticMemberextends.js", "original": {"line": 9, "column": 21}}, {"generated": {"line": 8, "column": 24}, "source": "src/staticMemberextends.js", "original": {"line": 9, "column": 24}}, {"generated": {"line": 8, "column": 36}, "source": "src/staticMemberextends.js", "original": {"line": 9, "column": 36}}, {"generated": {"line": 9, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 2}, "name": "console"}, {"generated": {"line": 9, "column": 9}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 9}}, {"generated": {"line": 9, "column": 10}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 10}, "name": "log"}, {"generated": {"line": 9, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 13}}, {"generated": {"line": 9, "column": 14}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 14}}, {"generated": {"line": 9, "column": 21}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 21}}, {"generated": {"line": 9, "column": 22}, "source": "src/staticMemberextends.js", "original": {"line": 10, "column": 22}}, {"generated": {"line": 10, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 11, "column": 0}}, {"generated": {"line": 10, "column": 1}, "source": "src/staticMemberextends.js", "original": {"line": 11, "column": 1}}, {"generated": {"line": 11, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 0}, "name": "People"}, {"generated": {"line": 11, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 6}}, {"generated": {"line": 11, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 7}, "name": "prototype"}, {"generated": {"line": 11, "column": 16}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 16}}, {"generated": {"line": 11, "column": 17}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 17}, "name": "doEat"}, {"generated": {"line": 11, "column": 22}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 22}}, {"generated": {"line": 11, "column": 25}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 25}}, {"generated": {"line": 11, "column": 37}, "source": "src/staticMemberextends.js", "original": {"line": 13, "column": 37}}, {"generated": {"line": 12, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 14, "column": 2}}, {"generated": {"line": 13, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 2}, "name": "console"}, {"generated": {"line": 13, "column": 9}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 9}}, {"generated": {"line": 13, "column": 10}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 10}, "name": "log"}, {"generated": {"line": 13, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 13}}, {"generated": {"line": 13, "column": 14}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 14}}, {"generated": {"line": 13, "column": 18}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 18}}, {"generated": {"line": 13, "column": 19}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 19}, "name": "name"}, {"generated": {"line": 13, "column": 23}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 23}}, {"generated": {"line": 13, "column": 26}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 26}}, {"generated": {"line": 13, "column": 33}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 33}}, {"generated": {"line": 13, "column": 34}, "source": "src/staticMemberextends.js", "original": {"line": 15, "column": 34}}, {"generated": {"line": 14, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 16, "column": 0}}, {"generated": {"line": 14, "column": 1}, "source": "src/staticMemberextends.js", "original": {"line": 16, "column": 1}}, {"generated": {"line": 15, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 0}}, {"generated": {"line": 15, "column": 4}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 4}, "name": "people"}, {"generated": {"line": 15, "column": 10}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 10}}, {"generated": {"line": 15, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 13}}, {"generated": {"line": 15, "column": 17}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 17}, "name": "People"}, {"generated": {"line": 15, "column": 23}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 23}}, {"generated": {"line": 15, "column": 24}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 24}}, {"generated": {"line": 15, "column": 28}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 28}}, {"generated": {"line": 15, "column": 30}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 30}}, {"generated": {"line": 15, "column": 33}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 33}}, {"generated": {"line": 15, "column": 35}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 35}}, {"generated": {"line": 15, "column": 48}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 48}}, {"generated": {"line": 15, "column": 49}, "source": "src/staticMemberextends.js", "original": {"line": 18, "column": 49}}, {"generated": {"line": 16, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 0}}, {"generated": {"line": 16, "column": 4}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 4}, "name": "ChinesePeople"}, {"generated": {"line": 16, "column": 17}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 17}}, {"generated": {"line": 16, "column": 20}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 20}}, {"generated": {"line": 16, "column": 29}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 4}, "name": "ChinesePeople"}, {"generated": {"line": 16, "column": 42}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 17}, "name": "ChinesePeople"}, {"generated": {"line": 16, "column": 43}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 30}, "name": "name"}, {"generated": {"line": 16, "column": 47}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 34}}, {"generated": {"line": 16, "column": 49}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 36}, "name": "sex"}, {"generated": {"line": 16, "column": 52}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 39}}, {"generated": {"line": 16, "column": 54}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 41}, "name": "phone"}, {"generated": {"line": 16, "column": 59}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 46}}, {"generated": {"line": 16, "column": 61}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 48}, "name": "language"}, {"generated": {"line": 16, "column": 69}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 56}}, {"generated": {"line": 16, "column": 71}, "source": "src/staticMemberextends.js", "original": {"line": 20, "column": 58}}, {"generated": {"line": 17, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 21, "column": 2}}, {"generated": {"line": 18, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 2}, "name": "People"}, {"generated": {"line": 18, "column": 8}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 8}}, {"generated": {"line": 18, "column": 9}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 9}, "name": "apply"}, {"generated": {"line": 18, "column": 14}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 14}}, {"generated": {"line": 18, "column": 15}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 15}}, {"generated": {"line": 18, "column": 19}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 19}}, {"generated": {"line": 18, "column": 21}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 21}}, {"generated": {"line": 18, "column": 22}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 22}, "name": "name"}, {"generated": {"line": 18, "column": 26}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 26}}, {"generated": {"line": 18, "column": 28}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 28}, "name": "sex"}, {"generated": {"line": 18, "column": 31}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 31}}, {"generated": {"line": 18, "column": 33}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 33}, "name": "phone"}, {"generated": {"line": 18, "column": 38}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 38}}, {"generated": {"line": 18, "column": 39}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 39}}, {"generated": {"line": 18, "column": 40}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 40}}, {"generated": {"line": 18, "column": 41}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 41}}, {"generated": {"line": 18, "column": 42}, "source": "src/staticMemberextends.js", "original": {"line": 22, "column": 42}}, {"generated": {"line": 19, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 23, "column": 2}}, {"generated": {"line": 19, "column": 6}, "source": "src/staticMemberextends.js", "original": {"line": 23, "column": 6}}, {"generated": {"line": 19, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 23, "column": 7}, "name": "language"}, {"generated": {"line": 19, "column": 15}, "source": "src/staticMemberextends.js", "original": {"line": 23, "column": 15}}, {"generated": {"line": 19, "column": 18}, "source": "src/staticMemberextends.js", "original": {"line": 23, "column": 18}, "name": "language"}, {"generated": {"line": 19, "column": 26}, "source": "src/staticMemberextends.js", "original": {"line": 23, "column": 26}}, {"generated": {"line": 20, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 24, "column": 0}}, {"generated": {"line": 20, "column": 1}, "source": "src/staticMemberextends.js", "original": {"line": 24, "column": 1}}, {"generated": {"line": 21, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 0}, "name": "ChinesePeople"}, {"generated": {"line": 21, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 13}}, {"generated": {"line": 21, "column": 14}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 14}, "name": "prototype"}, {"generated": {"line": 21, "column": 23}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 23}}, {"generated": {"line": 21, "column": 24}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 24}, "name": "drink"}, {"generated": {"line": 21, "column": 29}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 29}}, {"generated": {"line": 21, "column": 32}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 32}}, {"generated": {"line": 21, "column": 44}, "source": "src/staticMemberextends.js", "original": {"line": 26, "column": 44}}, {"generated": {"line": 22, "column": 2}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 2}, "name": "console"}, {"generated": {"line": 22, "column": 9}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 9}}, {"generated": {"line": 22, "column": 10}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 10}, "name": "log"}, {"generated": {"line": 22, "column": 13}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 13}}, {"generated": {"line": 22, "column": 14}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 14}}, {"generated": {"line": 22, "column": 18}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 18}}, {"generated": {"line": 22, "column": 19}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 19}, "name": "name"}, {"generated": {"line": 22, "column": 23}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 23}}, {"generated": {"line": 22, "column": 26}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 26}}, {"generated": {"line": 22, "column": 33}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 33}}, {"generated": {"line": 22, "column": 34}, "source": "src/staticMemberextends.js", "original": {"line": 27, "column": 34}}, {"generated": {"line": 23, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 28, "column": 0}}, {"generated": {"line": 23, "column": 1}, "source": "src/staticMemberextends.js", "original": {"line": 28, "column": 1}}, {"generated": {"line": 24, "column": 0}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 0}, "name": "console"}, {"generated": {"line": 24, "column": 7}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 7}}, {"generated": {"line": 24, "column": 8}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 8}, "name": "log"}, {"generated": {"line": 24, "column": 11}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 11}}, {"generated": {"line": 24, "column": 12}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 12}, "name": "people"}, {"generated": {"line": 24, "column": 18}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 18}}, {"generated": {"line": 24, "column": 19}, "source": "src/staticMemberextends.js", "original": {"line": 30, "column": 19}}], "sources": {"src/staticMemberextends.js": "let People = function (name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n};\n\nPeople.count = 300; // 静态属性\nPeople.commonDescribe = function () {\n  console.log(\"这是一个人\");\n};\n\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\n\nlet people = new People(\"张三\", \"男\", \"13888888888\");\n\nlet ChinesePeople = function (name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n};\n\nChinesePeople.prototype.drink = function () {\n  console.log(this.name + \"喝水...\");\n};\n\nconsole.log(people);\n"}, "lineCount": null}}, "error": null, "hash": "06eb32eb03b414edfa5dbc8857ed2acd", "cacheData": {"env": {}}}