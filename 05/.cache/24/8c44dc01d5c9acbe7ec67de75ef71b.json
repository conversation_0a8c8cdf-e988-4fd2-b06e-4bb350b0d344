{"id": "src/qquser.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "function QQUser(qq, password) {\n  this.qq = qq;\n  this.password = password;\n}\nconsole.log(QQUser.prototype); // true"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/qquser.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 1, "column": 9}, "source": "src/qquser.js", "original": {"line": 1, "column": 9}, "name": "QQUser"}, {"generated": {"line": 1, "column": 15}, "source": "src/qquser.js", "original": {"line": 1, "column": 15}, "name": "QQUser"}, {"generated": {"line": 1, "column": 16}, "source": "src/qquser.js", "original": {"line": 1, "column": 16}, "name": "qq"}, {"generated": {"line": 1, "column": 18}, "source": "src/qquser.js", "original": {"line": 1, "column": 18}}, {"generated": {"line": 1, "column": 20}, "source": "src/qquser.js", "original": {"line": 1, "column": 20}, "name": "password"}, {"generated": {"line": 1, "column": 28}, "source": "src/qquser.js", "original": {"line": 1, "column": 28}}, {"generated": {"line": 1, "column": 30}, "source": "src/qquser.js", "original": {"line": 1, "column": 30}}, {"generated": {"line": 2, "column": 2}, "source": "src/qquser.js", "original": {"line": 2, "column": 2}}, {"generated": {"line": 2, "column": 6}, "source": "src/qquser.js", "original": {"line": 2, "column": 6}}, {"generated": {"line": 2, "column": 7}, "source": "src/qquser.js", "original": {"line": 2, "column": 7}, "name": "qq"}, {"generated": {"line": 2, "column": 9}, "source": "src/qquser.js", "original": {"line": 2, "column": 9}}, {"generated": {"line": 2, "column": 12}, "source": "src/qquser.js", "original": {"line": 2, "column": 12}, "name": "qq"}, {"generated": {"line": 2, "column": 14}, "source": "src/qquser.js", "original": {"line": 2, "column": 14}}, {"generated": {"line": 3, "column": 2}, "source": "src/qquser.js", "original": {"line": 3, "column": 2}}, {"generated": {"line": 3, "column": 6}, "source": "src/qquser.js", "original": {"line": 3, "column": 6}}, {"generated": {"line": 3, "column": 7}, "source": "src/qquser.js", "original": {"line": 3, "column": 7}, "name": "password"}, {"generated": {"line": 3, "column": 15}, "source": "src/qquser.js", "original": {"line": 3, "column": 15}}, {"generated": {"line": 3, "column": 18}, "source": "src/qquser.js", "original": {"line": 3, "column": 18}, "name": "password"}, {"generated": {"line": 3, "column": 26}, "source": "src/qquser.js", "original": {"line": 3, "column": 26}}, {"generated": {"line": 4, "column": 0}, "source": "src/qquser.js", "original": {"line": 4, "column": 0}}, {"generated": {"line": 5, "column": 0}, "source": "src/qquser.js", "original": {"line": 6, "column": 0}, "name": "console"}, {"generated": {"line": 5, "column": 7}, "source": "src/qquser.js", "original": {"line": 6, "column": 7}}, {"generated": {"line": 5, "column": 8}, "source": "src/qquser.js", "original": {"line": 6, "column": 8}, "name": "log"}, {"generated": {"line": 5, "column": 11}, "source": "src/qquser.js", "original": {"line": 6, "column": 11}}, {"generated": {"line": 5, "column": 12}, "source": "src/qquser.js", "original": {"line": 6, "column": 12}, "name": "QQUser"}, {"generated": {"line": 5, "column": 18}, "source": "src/qquser.js", "original": {"line": 6, "column": 18}}, {"generated": {"line": 5, "column": 19}, "source": "src/qquser.js", "original": {"line": 6, "column": 19}, "name": "prototype"}, {"generated": {"line": 5, "column": 28}, "source": "src/qquser.js", "original": {"line": 6, "column": 28}}, {"generated": {"line": 5, "column": 29}, "source": "src/qquser.js", "original": {"line": 6, "column": 29}}, {"generated": {"line": 5, "column": 30}, "source": "src/qquser.js", "original": {"line": 6, "column": 30}}, {"generated": {"line": 5, "column": 31}, "source": "src/qquser.js", "original": {"line": 6, "column": 31}}], "sources": {"src/qquser.js": "function QQUser(qq, password) {\n  this.qq = qq;\n  this.password = password;\n}\n\nconsole.log(QQUser.prototype); // true\n"}, "lineCount": null}}, "error": null, "hash": "0436c8b5047c65ef8d38d7b5c24994b7", "cacheData": {"env": {}}}