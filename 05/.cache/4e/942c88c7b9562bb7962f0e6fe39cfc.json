{"id": "src/parasitComblinherit4.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "// 寄生组合继承(最佳实践--最常用的继承方式)\nfunction Parent(name, age) {\n  this.name = name;\n  this.age = age;\n  // 通过原型链继承+构造函数继承 构造函数执行了两次\n  console.log(\"this name : \", this.name);\n}\nParent.prototype.say = function () {\n  console.log(this.name);\n};\nfunction Son(name, age, hobby) {\n  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承\n  Parent.call(this, name, age);\n  this.hobby = hobby;\n}\nfunction middle() {\n  this.count = 22;\n}\nmiddle.prototype = Parent.prototype;\nSon.prototype = new middle();\n\n// 为 Son 原型添加自己的方法\nSon.prototype.showHobby = function () {\n  console.log(this.hobby);\n};\n\n// 使用示例\nvar son = new Son(\"小明\", 12, \"打篮球\");\nson.say(); // 输出: 小明\nson.showHobby(); // 输出: 打篮球\n\n// 此时Son.prototype.constructor指向的是Parent的构造函数空间\nconsole.log(Son.prototype.constructor);\n\n// 修复 Son.prototype.constructor 指向问题\nSon.prototype.constructor = Son;\nvar son2 = new Son(\"小明\", 12, \"打篮球\");\nconsole.log(Son.prototype.constructor);\nconsole.log(\"-----\", son2);"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 2, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 0}}, {"generated": {"line": 2, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 9}, "name": "Parent"}, {"generated": {"line": 2, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 15}, "name": "Parent"}, {"generated": {"line": 2, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 16}, "name": "name"}, {"generated": {"line": 2, "column": 20}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 20}}, {"generated": {"line": 2, "column": 22}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 22}, "name": "age"}, {"generated": {"line": 2, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 25}}, {"generated": {"line": 2, "column": 27}, "source": "src/parasitComblinherit4.js", "original": {"line": 2, "column": 27}}, {"generated": {"line": 3, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 3, "column": 2}}, {"generated": {"line": 3, "column": 6}, "source": "src/parasitComblinherit4.js", "original": {"line": 3, "column": 6}}, {"generated": {"line": 3, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 3, "column": 7}, "name": "name"}, {"generated": {"line": 3, "column": 11}, "source": "src/parasitComblinherit4.js", "original": {"line": 3, "column": 11}}, {"generated": {"line": 3, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 3, "column": 14}, "name": "name"}, {"generated": {"line": 3, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 3, "column": 18}}, {"generated": {"line": 4, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 4, "column": 2}}, {"generated": {"line": 4, "column": 6}, "source": "src/parasitComblinherit4.js", "original": {"line": 4, "column": 6}}, {"generated": {"line": 4, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 4, "column": 7}, "name": "age"}, {"generated": {"line": 4, "column": 10}, "source": "src/parasitComblinherit4.js", "original": {"line": 4, "column": 10}}, {"generated": {"line": 4, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 4, "column": 13}, "name": "age"}, {"generated": {"line": 4, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 4, "column": 16}}, {"generated": {"line": 5, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 5, "column": 2}}, {"generated": {"line": 6, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 2}, "name": "console"}, {"generated": {"line": 6, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 9}}, {"generated": {"line": 6, "column": 10}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 10}, "name": "log"}, {"generated": {"line": 6, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 13}}, {"generated": {"line": 6, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 14}}, {"generated": {"line": 6, "column": 28}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 28}}, {"generated": {"line": 6, "column": 30}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 30}}, {"generated": {"line": 6, "column": 34}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 34}}, {"generated": {"line": 6, "column": 35}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 35}, "name": "name"}, {"generated": {"line": 6, "column": 39}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 39}}, {"generated": {"line": 6, "column": 40}, "source": "src/parasitComblinherit4.js", "original": {"line": 6, "column": 40}}, {"generated": {"line": 7, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 7, "column": 0}}, {"generated": {"line": 8, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 0}, "name": "Parent"}, {"generated": {"line": 8, "column": 6}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 6}}, {"generated": {"line": 8, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 7}, "name": "prototype"}, {"generated": {"line": 8, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 16}}, {"generated": {"line": 8, "column": 17}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 17}, "name": "say"}, {"generated": {"line": 8, "column": 20}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 20}}, {"generated": {"line": 8, "column": 23}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 23}}, {"generated": {"line": 8, "column": 35}, "source": "src/parasitComblinherit4.js", "original": {"line": 9, "column": 35}}, {"generated": {"line": 9, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 2}, "name": "console"}, {"generated": {"line": 9, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 9}}, {"generated": {"line": 9, "column": 10}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 10}, "name": "log"}, {"generated": {"line": 9, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 13}}, {"generated": {"line": 9, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 14}}, {"generated": {"line": 9, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 18}}, {"generated": {"line": 9, "column": 19}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 19}, "name": "name"}, {"generated": {"line": 9, "column": 23}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 23}}, {"generated": {"line": 9, "column": 24}, "source": "src/parasitComblinherit4.js", "original": {"line": 10, "column": 24}}, {"generated": {"line": 10, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 11, "column": 0}}, {"generated": {"line": 10, "column": 1}, "source": "src/parasitComblinherit4.js", "original": {"line": 11, "column": 1}}, {"generated": {"line": 11, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 0}}, {"generated": {"line": 11, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 9}, "name": "Son"}, {"generated": {"line": 11, "column": 12}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 12}, "name": "Son"}, {"generated": {"line": 11, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 13}, "name": "name"}, {"generated": {"line": 11, "column": 17}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 17}}, {"generated": {"line": 11, "column": 19}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 19}, "name": "age"}, {"generated": {"line": 11, "column": 22}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 22}}, {"generated": {"line": 11, "column": 24}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 24}, "name": "hobby"}, {"generated": {"line": 11, "column": 29}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 29}}, {"generated": {"line": 11, "column": 31}, "source": "src/parasitComblinherit4.js", "original": {"line": 13, "column": 31}}, {"generated": {"line": 12, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 14, "column": 2}}, {"generated": {"line": 13, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 2}, "name": "Parent"}, {"generated": {"line": 13, "column": 8}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 8}}, {"generated": {"line": 13, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 9}, "name": "call"}, {"generated": {"line": 13, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 13}}, {"generated": {"line": 13, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 14}}, {"generated": {"line": 13, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 18}}, {"generated": {"line": 13, "column": 20}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 20}, "name": "name"}, {"generated": {"line": 13, "column": 24}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 24}}, {"generated": {"line": 13, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 26}, "name": "age"}, {"generated": {"line": 13, "column": 29}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 29}}, {"generated": {"line": 13, "column": 30}, "source": "src/parasitComblinherit4.js", "original": {"line": 15, "column": 30}}, {"generated": {"line": 14, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 16, "column": 2}}, {"generated": {"line": 14, "column": 6}, "source": "src/parasitComblinherit4.js", "original": {"line": 16, "column": 6}}, {"generated": {"line": 14, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 16, "column": 7}, "name": "hobby"}, {"generated": {"line": 14, "column": 12}, "source": "src/parasitComblinherit4.js", "original": {"line": 16, "column": 12}}, {"generated": {"line": 14, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 16, "column": 15}, "name": "hobby"}, {"generated": {"line": 14, "column": 20}, "source": "src/parasitComblinherit4.js", "original": {"line": 16, "column": 20}}, {"generated": {"line": 15, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 17, "column": 0}}, {"generated": {"line": 16, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 19, "column": 0}}, {"generated": {"line": 16, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 19, "column": 9}, "name": "middle"}, {"generated": {"line": 16, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 19, "column": 15}, "name": "middle"}, {"generated": {"line": 16, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 19, "column": 15}}, {"generated": {"line": 16, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 19, "column": 18}}, {"generated": {"line": 17, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 20, "column": 2}}, {"generated": {"line": 17, "column": 6}, "source": "src/parasitComblinherit4.js", "original": {"line": 20, "column": 6}}, {"generated": {"line": 17, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 20, "column": 7}, "name": "count"}, {"generated": {"line": 17, "column": 12}, "source": "src/parasitComblinherit4.js", "original": {"line": 20, "column": 12}}, {"generated": {"line": 17, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 20, "column": 15}}, {"generated": {"line": 17, "column": 17}, "source": "src/parasitComblinherit4.js", "original": {"line": 20, "column": 17}}, {"generated": {"line": 18, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 21, "column": 0}}, {"generated": {"line": 19, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 0}, "name": "middle"}, {"generated": {"line": 19, "column": 6}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 6}}, {"generated": {"line": 19, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 7}, "name": "prototype"}, {"generated": {"line": 19, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 16}}, {"generated": {"line": 19, "column": 19}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 19}, "name": "Parent"}, {"generated": {"line": 19, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 25}}, {"generated": {"line": 19, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 26}, "name": "prototype"}, {"generated": {"line": 19, "column": 35}, "source": "src/parasitComblinherit4.js", "original": {"line": 22, "column": 35}}, {"generated": {"line": 20, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 0}, "name": "Son"}, {"generated": {"line": 20, "column": 3}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 3}}, {"generated": {"line": 20, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 4}, "name": "prototype"}, {"generated": {"line": 20, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 13}}, {"generated": {"line": 20, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 16}}, {"generated": {"line": 20, "column": 20}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 20}, "name": "middle"}, {"generated": {"line": 20, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 26}}, {"generated": {"line": 20, "column": 27}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 27}}, {"generated": {"line": 20, "column": 28}, "source": "src/parasitComblinherit4.js", "original": {"line": 24, "column": 28}}, {"generated": {"line": 22, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 26, "column": 0}}, {"generated": {"line": 23, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 0}, "name": "Son"}, {"generated": {"line": 23, "column": 3}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 3}}, {"generated": {"line": 23, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 4}, "name": "prototype"}, {"generated": {"line": 23, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 13}}, {"generated": {"line": 23, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 14}, "name": "showH<PERSON><PERSON>"}, {"generated": {"line": 23, "column": 23}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 23}}, {"generated": {"line": 23, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 26}}, {"generated": {"line": 23, "column": 38}, "source": "src/parasitComblinherit4.js", "original": {"line": 27, "column": 38}}, {"generated": {"line": 24, "column": 2}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 2}, "name": "console"}, {"generated": {"line": 24, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 9}}, {"generated": {"line": 24, "column": 10}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 10}, "name": "log"}, {"generated": {"line": 24, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 13}}, {"generated": {"line": 24, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 14}}, {"generated": {"line": 24, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 18}}, {"generated": {"line": 24, "column": 19}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 19}, "name": "hobby"}, {"generated": {"line": 24, "column": 24}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 24}}, {"generated": {"line": 24, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 28, "column": 25}}, {"generated": {"line": 25, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 29, "column": 0}}, {"generated": {"line": 25, "column": 1}, "source": "src/parasitComblinherit4.js", "original": {"line": 29, "column": 1}}, {"generated": {"line": 27, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 31, "column": 0}}, {"generated": {"line": 28, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 0}}, {"generated": {"line": 28, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 6}, "name": "son"}, {"generated": {"line": 28, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 9}}, {"generated": {"line": 28, "column": 10}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 12}}, {"generated": {"line": 28, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 16}, "name": "Son"}, {"generated": {"line": 28, "column": 17}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 19}}, {"generated": {"line": 28, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 20}}, {"generated": {"line": 28, "column": 22}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 24}}, {"generated": {"line": 28, "column": 24}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 26}}, {"generated": {"line": 28, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 28}}, {"generated": {"line": 28, "column": 28}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 30}}, {"generated": {"line": 28, "column": 33}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 35}}, {"generated": {"line": 28, "column": 34}, "source": "src/parasitComblinherit4.js", "original": {"line": 32, "column": 36}}, {"generated": {"line": 29, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 0}, "name": "son"}, {"generated": {"line": 29, "column": 3}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 3}}, {"generated": {"line": 29, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 4}, "name": "say"}, {"generated": {"line": 29, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 7}}, {"generated": {"line": 29, "column": 8}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 8}}, {"generated": {"line": 29, "column": 9}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 9}}, {"generated": {"line": 29, "column": 10}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 10}}, {"generated": {"line": 29, "column": 11}, "source": "src/parasitComblinherit4.js", "original": {"line": 33, "column": 11}}, {"generated": {"line": 30, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 0}, "name": "son"}, {"generated": {"line": 30, "column": 3}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 3}}, {"generated": {"line": 30, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 4}, "name": "showH<PERSON><PERSON>"}, {"generated": {"line": 30, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 13}}, {"generated": {"line": 30, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 14}}, {"generated": {"line": 30, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 15}}, {"generated": {"line": 30, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 16}}, {"generated": {"line": 30, "column": 17}, "source": "src/parasitComblinherit4.js", "original": {"line": 34, "column": 17}}, {"generated": {"line": 32, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 36, "column": 0}}, {"generated": {"line": 33, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 0}, "name": "console"}, {"generated": {"line": 33, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 7}}, {"generated": {"line": 33, "column": 8}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 8}, "name": "log"}, {"generated": {"line": 33, "column": 11}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 11}}, {"generated": {"line": 33, "column": 12}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 12}, "name": "Son"}, {"generated": {"line": 33, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 15}}, {"generated": {"line": 33, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 16}, "name": "prototype"}, {"generated": {"line": 33, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 25}}, {"generated": {"line": 33, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 26}, "name": "constructor"}, {"generated": {"line": 33, "column": 37}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 37}}, {"generated": {"line": 33, "column": 38}, "source": "src/parasitComblinherit4.js", "original": {"line": 37, "column": 38}}, {"generated": {"line": 35, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 39, "column": 0}}, {"generated": {"line": 36, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 0}, "name": "Son"}, {"generated": {"line": 36, "column": 3}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 3}}, {"generated": {"line": 36, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 4}, "name": "prototype"}, {"generated": {"line": 36, "column": 13}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 13}}, {"generated": {"line": 36, "column": 14}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 14}, "name": "constructor"}, {"generated": {"line": 36, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 25}}, {"generated": {"line": 36, "column": 28}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 28}, "name": "Son"}, {"generated": {"line": 36, "column": 31}, "source": "src/parasitComblinherit4.js", "original": {"line": 40, "column": 31}}, {"generated": {"line": 37, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 0}}, {"generated": {"line": 37, "column": 4}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 6}, "name": "son2"}, {"generated": {"line": 37, "column": 8}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 10}}, {"generated": {"line": 37, "column": 11}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 13}}, {"generated": {"line": 37, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 17}, "name": "Son"}, {"generated": {"line": 37, "column": 18}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 20}}, {"generated": {"line": 37, "column": 19}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 21}}, {"generated": {"line": 37, "column": 23}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 25}}, {"generated": {"line": 37, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 27}}, {"generated": {"line": 37, "column": 27}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 29}}, {"generated": {"line": 37, "column": 29}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 31}}, {"generated": {"line": 37, "column": 34}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 36}}, {"generated": {"line": 37, "column": 35}, "source": "src/parasitComblinherit4.js", "original": {"line": 41, "column": 37}}, {"generated": {"line": 38, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 0}, "name": "console"}, {"generated": {"line": 38, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 7}}, {"generated": {"line": 38, "column": 8}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 8}, "name": "log"}, {"generated": {"line": 38, "column": 11}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 11}}, {"generated": {"line": 38, "column": 12}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 12}, "name": "Son"}, {"generated": {"line": 38, "column": 15}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 15}}, {"generated": {"line": 38, "column": 16}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 16}, "name": "prototype"}, {"generated": {"line": 38, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 25}}, {"generated": {"line": 38, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 26}, "name": "constructor"}, {"generated": {"line": 38, "column": 37}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 37}}, {"generated": {"line": 38, "column": 38}, "source": "src/parasitComblinherit4.js", "original": {"line": 43, "column": 38}}, {"generated": {"line": 39, "column": 0}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 0}, "name": "console"}, {"generated": {"line": 39, "column": 7}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 7}}, {"generated": {"line": 39, "column": 8}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 8}, "name": "log"}, {"generated": {"line": 39, "column": 11}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 11}}, {"generated": {"line": 39, "column": 12}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 12}}, {"generated": {"line": 39, "column": 19}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 19}}, {"generated": {"line": 39, "column": 21}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 21}, "name": "son2"}, {"generated": {"line": 39, "column": 25}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 25}}, {"generated": {"line": 39, "column": 26}, "source": "src/parasitComblinherit4.js", "original": {"line": 44, "column": 26}}], "sources": {"src/parasitComblinherit4.js": "// 寄生组合继承(最佳实践--最常用的继承方式)\nfunction Parent(name, age) {\n  this.name = name;\n  this.age = age;\n  // 通过原型链继承+构造函数继承 构造函数执行了两次\n  console.log(\"this name : \", this.name);\n}\n\nParent.prototype.say = function () {\n  console.log(this.name);\n};\n\nfunction Son(name, age, hobby) {\n  // 调用父类构造函数，绑定 this 为 Son 实例，实现属性继承\n  Parent.call(this, name, age);\n  this.hobby = hobby;\n}\n\nfunction middle() {\n  this.count = 22;\n}\nmiddle.prototype = Parent.prototype;\n\nSon.prototype = new middle();\n\n// 为 Son 原型添加自己的方法\nSon.prototype.showHobby = function () {\n  console.log(this.hobby);\n};\n\n// 使用示例\nconst son = new Son(\"小明\", 12, \"打篮球\");\nson.say(); // 输出: 小明\nson.showHobby(); // 输出: 打篮球\n\n// 此时Son.prototype.constructor指向的是Parent的构造函数空间\nconsole.log(Son.prototype.constructor);\n\n// 修复 Son.prototype.constructor 指向问题\nSon.prototype.constructor = Son;\nconst son2 = new Son(\"小明\", 12, \"打篮球\");\n\nconsole.log(Son.prototype.constructor);\nconsole.log(\"-----\", son2);\n"}, "lineCount": null}}, "error": null, "hash": "ebb806be4646bd94532f6aff2d93aa5d", "cacheData": {"env": {}}}