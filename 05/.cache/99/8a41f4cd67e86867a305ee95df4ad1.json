{"id": "src/ttt.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "function Middle() {\n  this.constructor = Son;\n}\nfunction Father() {}\nFather.prototype.say = function () {\n  console.log(\"hello\");\n};\nfunction Son() {}\nMiddle.prototype = Father.prototype;\nSon.prototype = new Middle();\nconsole.log(Son.prototype.constructor);\nvar son = new Son();\nconsole.log(son.say());\nconsole.log(son.__proto__.constructor);\nconsole.log(son);"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/ttt.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 1, "column": 9}, "source": "src/ttt.js", "original": {"line": 1, "column": 9}, "name": "Middle"}, {"generated": {"line": 1, "column": 15}, "source": "src/ttt.js", "original": {"line": 1, "column": 15}, "name": "Middle"}, {"generated": {"line": 1, "column": 16}, "source": "src/ttt.js", "original": {"line": 1, "column": 15}}, {"generated": {"line": 1, "column": 18}, "source": "src/ttt.js", "original": {"line": 1, "column": 18}}, {"generated": {"line": 2, "column": 2}, "source": "src/ttt.js", "original": {"line": 2, "column": 2}}, {"generated": {"line": 2, "column": 6}, "source": "src/ttt.js", "original": {"line": 2, "column": 6}}, {"generated": {"line": 2, "column": 7}, "source": "src/ttt.js", "original": {"line": 2, "column": 7}, "name": "constructor"}, {"generated": {"line": 2, "column": 18}, "source": "src/ttt.js", "original": {"line": 2, "column": 18}}, {"generated": {"line": 2, "column": 21}, "source": "src/ttt.js", "original": {"line": 2, "column": 21}, "name": "Son"}, {"generated": {"line": 2, "column": 24}, "source": "src/ttt.js", "original": {"line": 2, "column": 24}}, {"generated": {"line": 3, "column": 0}, "source": "src/ttt.js", "original": {"line": 3, "column": 0}}, {"generated": {"line": 4, "column": 0}, "source": "src/ttt.js", "original": {"line": 5, "column": 0}}, {"generated": {"line": 4, "column": 9}, "source": "src/ttt.js", "original": {"line": 5, "column": 9}, "name": "Father"}, {"generated": {"line": 4, "column": 15}, "source": "src/ttt.js", "original": {"line": 5, "column": 15}, "name": "Father"}, {"generated": {"line": 4, "column": 16}, "source": "src/ttt.js", "original": {"line": 5, "column": 15}}, {"generated": {"line": 4, "column": 18}, "source": "src/ttt.js", "original": {"line": 5, "column": 18}}, {"generated": {"line": 4, "column": 19}, "source": "src/ttt.js", "original": {"line": 5, "column": 19}}, {"generated": {"line": 5, "column": 0}, "source": "src/ttt.js", "original": {"line": 7, "column": 0}, "name": "Father"}, {"generated": {"line": 5, "column": 6}, "source": "src/ttt.js", "original": {"line": 7, "column": 6}}, {"generated": {"line": 5, "column": 7}, "source": "src/ttt.js", "original": {"line": 7, "column": 7}, "name": "prototype"}, {"generated": {"line": 5, "column": 16}, "source": "src/ttt.js", "original": {"line": 7, "column": 16}}, {"generated": {"line": 5, "column": 17}, "source": "src/ttt.js", "original": {"line": 7, "column": 17}, "name": "say"}, {"generated": {"line": 5, "column": 20}, "source": "src/ttt.js", "original": {"line": 7, "column": 20}}, {"generated": {"line": 5, "column": 23}, "source": "src/ttt.js", "original": {"line": 7, "column": 23}}, {"generated": {"line": 5, "column": 35}, "source": "src/ttt.js", "original": {"line": 7, "column": 35}}, {"generated": {"line": 6, "column": 2}, "source": "src/ttt.js", "original": {"line": 8, "column": 2}, "name": "console"}, {"generated": {"line": 6, "column": 9}, "source": "src/ttt.js", "original": {"line": 8, "column": 9}}, {"generated": {"line": 6, "column": 10}, "source": "src/ttt.js", "original": {"line": 8, "column": 10}, "name": "log"}, {"generated": {"line": 6, "column": 13}, "source": "src/ttt.js", "original": {"line": 8, "column": 13}}, {"generated": {"line": 6, "column": 14}, "source": "src/ttt.js", "original": {"line": 8, "column": 14}}, {"generated": {"line": 6, "column": 21}, "source": "src/ttt.js", "original": {"line": 8, "column": 21}}, {"generated": {"line": 6, "column": 22}, "source": "src/ttt.js", "original": {"line": 8, "column": 22}}, {"generated": {"line": 7, "column": 0}, "source": "src/ttt.js", "original": {"line": 9, "column": 0}}, {"generated": {"line": 7, "column": 1}, "source": "src/ttt.js", "original": {"line": 9, "column": 1}}, {"generated": {"line": 8, "column": 0}, "source": "src/ttt.js", "original": {"line": 11, "column": 0}}, {"generated": {"line": 8, "column": 9}, "source": "src/ttt.js", "original": {"line": 11, "column": 9}, "name": "Son"}, {"generated": {"line": 8, "column": 12}, "source": "src/ttt.js", "original": {"line": 11, "column": 12}, "name": "Son"}, {"generated": {"line": 8, "column": 13}, "source": "src/ttt.js", "original": {"line": 11, "column": 12}}, {"generated": {"line": 8, "column": 15}, "source": "src/ttt.js", "original": {"line": 11, "column": 15}}, {"generated": {"line": 8, "column": 16}, "source": "src/ttt.js", "original": {"line": 11, "column": 16}}, {"generated": {"line": 9, "column": 0}, "source": "src/ttt.js", "original": {"line": 13, "column": 0}, "name": "Middle"}, {"generated": {"line": 9, "column": 6}, "source": "src/ttt.js", "original": {"line": 13, "column": 6}}, {"generated": {"line": 9, "column": 7}, "source": "src/ttt.js", "original": {"line": 13, "column": 7}, "name": "prototype"}, {"generated": {"line": 9, "column": 16}, "source": "src/ttt.js", "original": {"line": 13, "column": 16}}, {"generated": {"line": 9, "column": 19}, "source": "src/ttt.js", "original": {"line": 13, "column": 19}, "name": "Father"}, {"generated": {"line": 9, "column": 25}, "source": "src/ttt.js", "original": {"line": 13, "column": 25}}, {"generated": {"line": 9, "column": 26}, "source": "src/ttt.js", "original": {"line": 13, "column": 26}, "name": "prototype"}, {"generated": {"line": 9, "column": 35}, "source": "src/ttt.js", "original": {"line": 13, "column": 35}}, {"generated": {"line": 10, "column": 0}, "source": "src/ttt.js", "original": {"line": 15, "column": 0}, "name": "Son"}, {"generated": {"line": 10, "column": 3}, "source": "src/ttt.js", "original": {"line": 15, "column": 3}}, {"generated": {"line": 10, "column": 4}, "source": "src/ttt.js", "original": {"line": 15, "column": 4}, "name": "prototype"}, {"generated": {"line": 10, "column": 13}, "source": "src/ttt.js", "original": {"line": 15, "column": 13}}, {"generated": {"line": 10, "column": 16}, "source": "src/ttt.js", "original": {"line": 15, "column": 16}}, {"generated": {"line": 10, "column": 20}, "source": "src/ttt.js", "original": {"line": 15, "column": 20}, "name": "Middle"}, {"generated": {"line": 10, "column": 26}, "source": "src/ttt.js", "original": {"line": 15, "column": 26}}, {"generated": {"line": 10, "column": 27}, "source": "src/ttt.js", "original": {"line": 15, "column": 27}}, {"generated": {"line": 10, "column": 28}, "source": "src/ttt.js", "original": {"line": 15, "column": 28}}, {"generated": {"line": 11, "column": 0}, "source": "src/ttt.js", "original": {"line": 16, "column": 0}, "name": "console"}, {"generated": {"line": 11, "column": 7}, "source": "src/ttt.js", "original": {"line": 16, "column": 7}}, {"generated": {"line": 11, "column": 8}, "source": "src/ttt.js", "original": {"line": 16, "column": 8}, "name": "log"}, {"generated": {"line": 11, "column": 11}, "source": "src/ttt.js", "original": {"line": 16, "column": 11}}, {"generated": {"line": 11, "column": 12}, "source": "src/ttt.js", "original": {"line": 16, "column": 12}, "name": "Son"}, {"generated": {"line": 11, "column": 15}, "source": "src/ttt.js", "original": {"line": 16, "column": 15}}, {"generated": {"line": 11, "column": 16}, "source": "src/ttt.js", "original": {"line": 16, "column": 16}, "name": "prototype"}, {"generated": {"line": 11, "column": 25}, "source": "src/ttt.js", "original": {"line": 16, "column": 25}}, {"generated": {"line": 11, "column": 26}, "source": "src/ttt.js", "original": {"line": 16, "column": 26}, "name": "constructor"}, {"generated": {"line": 11, "column": 37}, "source": "src/ttt.js", "original": {"line": 16, "column": 37}}, {"generated": {"line": 11, "column": 38}, "source": "src/ttt.js", "original": {"line": 16, "column": 38}}, {"generated": {"line": 12, "column": 0}, "source": "src/ttt.js", "original": {"line": 18, "column": 0}}, {"generated": {"line": 12, "column": 4}, "source": "src/ttt.js", "original": {"line": 18, "column": 4}, "name": "son"}, {"generated": {"line": 12, "column": 7}, "source": "src/ttt.js", "original": {"line": 18, "column": 7}}, {"generated": {"line": 12, "column": 10}, "source": "src/ttt.js", "original": {"line": 18, "column": 10}}, {"generated": {"line": 12, "column": 14}, "source": "src/ttt.js", "original": {"line": 18, "column": 14}, "name": "Son"}, {"generated": {"line": 12, "column": 17}, "source": "src/ttt.js", "original": {"line": 18, "column": 17}}, {"generated": {"line": 12, "column": 18}, "source": "src/ttt.js", "original": {"line": 18, "column": 18}}, {"generated": {"line": 12, "column": 19}, "source": "src/ttt.js", "original": {"line": 18, "column": 19}}, {"generated": {"line": 13, "column": 0}, "source": "src/ttt.js", "original": {"line": 19, "column": 0}, "name": "console"}, {"generated": {"line": 13, "column": 7}, "source": "src/ttt.js", "original": {"line": 19, "column": 7}}, {"generated": {"line": 13, "column": 8}, "source": "src/ttt.js", "original": {"line": 19, "column": 8}, "name": "log"}, {"generated": {"line": 13, "column": 11}, "source": "src/ttt.js", "original": {"line": 19, "column": 11}}, {"generated": {"line": 13, "column": 12}, "source": "src/ttt.js", "original": {"line": 19, "column": 12}, "name": "son"}, {"generated": {"line": 13, "column": 15}, "source": "src/ttt.js", "original": {"line": 19, "column": 15}}, {"generated": {"line": 13, "column": 16}, "source": "src/ttt.js", "original": {"line": 19, "column": 16}, "name": "say"}, {"generated": {"line": 13, "column": 19}, "source": "src/ttt.js", "original": {"line": 19, "column": 19}}, {"generated": {"line": 13, "column": 20}, "source": "src/ttt.js", "original": {"line": 19, "column": 20}}, {"generated": {"line": 13, "column": 21}, "source": "src/ttt.js", "original": {"line": 19, "column": 21}}, {"generated": {"line": 13, "column": 22}, "source": "src/ttt.js", "original": {"line": 19, "column": 22}}, {"generated": {"line": 14, "column": 0}, "source": "src/ttt.js", "original": {"line": 20, "column": 0}, "name": "console"}, {"generated": {"line": 14, "column": 7}, "source": "src/ttt.js", "original": {"line": 20, "column": 7}}, {"generated": {"line": 14, "column": 8}, "source": "src/ttt.js", "original": {"line": 20, "column": 8}, "name": "log"}, {"generated": {"line": 14, "column": 11}, "source": "src/ttt.js", "original": {"line": 20, "column": 11}}, {"generated": {"line": 14, "column": 12}, "source": "src/ttt.js", "original": {"line": 20, "column": 12}, "name": "son"}, {"generated": {"line": 14, "column": 15}, "source": "src/ttt.js", "original": {"line": 20, "column": 15}}, {"generated": {"line": 14, "column": 16}, "source": "src/ttt.js", "original": {"line": 20, "column": 16}, "name": "__proto__"}, {"generated": {"line": 14, "column": 25}, "source": "src/ttt.js", "original": {"line": 20, "column": 25}}, {"generated": {"line": 14, "column": 26}, "source": "src/ttt.js", "original": {"line": 20, "column": 26}, "name": "constructor"}, {"generated": {"line": 14, "column": 37}, "source": "src/ttt.js", "original": {"line": 20, "column": 37}}, {"generated": {"line": 14, "column": 38}, "source": "src/ttt.js", "original": {"line": 20, "column": 38}}, {"generated": {"line": 15, "column": 0}, "source": "src/ttt.js", "original": {"line": 22, "column": 0}, "name": "console"}, {"generated": {"line": 15, "column": 7}, "source": "src/ttt.js", "original": {"line": 22, "column": 7}}, {"generated": {"line": 15, "column": 8}, "source": "src/ttt.js", "original": {"line": 22, "column": 8}, "name": "log"}, {"generated": {"line": 15, "column": 11}, "source": "src/ttt.js", "original": {"line": 22, "column": 11}}, {"generated": {"line": 15, "column": 12}, "source": "src/ttt.js", "original": {"line": 22, "column": 12}, "name": "son"}, {"generated": {"line": 15, "column": 15}, "source": "src/ttt.js", "original": {"line": 22, "column": 15}}, {"generated": {"line": 15, "column": 16}, "source": "src/ttt.js", "original": {"line": 22, "column": 16}}], "sources": {"src/ttt.js": "function Middle() {\n  this.constructor = Son;\n}\n\nfunction Father() {}\n\nFather.prototype.say = function () {\n  console.log(\"hello\");\n};\n\nfunction Son() {}\n\nMiddle.prototype = Father.prototype;\n\nSon.prototype = new Middle();\nconsole.log(Son.prototype.constructor);\n\nvar son = new Son();\nconsole.log(son.say());\nconsole.log(son.__proto__.constructor);\n\nconsole.log(son);\n"}, "lineCount": null}}, "error": null, "hash": "4b848dbc0d8cddce044708e8487b9571", "cacheData": {"env": {}}}