{"id": "src/chinese_people.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "function People(name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n  this.say = function () {\n    console.log(\"say...\");\n  };\n}\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\nfunction ChinesePeople(name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n}\n// ChinesePeople.prototype = Object.create(People.prototype); // 原型链继承 （new People() 有很多缺点， Object.create(People.prototype) ）\n\n// 构建一个公用的寄生组合继承函数【最佳原型继承】- 即是Object.creae()的实现\nfunction createNewPrototypeObj(root, son) {\n  function Middle() {\n    this.count = 23;\n    // this.constructor = son; // 等同 ChinesePeople.prototype.constructor = ChinesePeople\n  }\n  Middle.prototype = root.prototype; // People.prototype;\n\n  var middle = new Middle();\n  return middle;\n}\n\n// 寄生组合继承\n// function midle() {}\n// midle.prototype = People.prototype; // 约等同于  Object.create(People.prototype)\n// ChinesePeople.prototype = new midle();\n\nChinesePeople.prototype = createNewPrototypeObj(People, ChinesePeople);\nChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步\nvar chinesePeople = new ChinesePeople(\"张三\", \"男\", \"13888888888\", \"中文\"); // 子类实例\nconsole.log(chinesePeople);\nchinesePeople.doEat();"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 1, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 9}, "name": "People"}, {"generated": {"line": 1, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 15}, "name": "People"}, {"generated": {"line": 1, "column": 16}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 16}, "name": "name"}, {"generated": {"line": 1, "column": 20}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 20}}, {"generated": {"line": 1, "column": 22}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 22}, "name": "sex"}, {"generated": {"line": 1, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 25}}, {"generated": {"line": 1, "column": 27}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 27}, "name": "phone"}, {"generated": {"line": 1, "column": 32}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 32}}, {"generated": {"line": 1, "column": 34}, "source": "src/chinese_people.js", "original": {"line": 1, "column": 34}}, {"generated": {"line": 2, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 2, "column": 2}}, {"generated": {"line": 3, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 2}}, {"generated": {"line": 3, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 6}}, {"generated": {"line": 3, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 7}, "name": "name"}, {"generated": {"line": 3, "column": 11}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 11}}, {"generated": {"line": 3, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 14}, "name": "name"}, {"generated": {"line": 3, "column": 18}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 18}}, {"generated": {"line": 3, "column": 19}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 19}}, {"generated": {"line": 3, "column": 20}, "source": "src/chinese_people.js", "original": {"line": 3, "column": 20}}, {"generated": {"line": 4, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 4, "column": 2}}, {"generated": {"line": 4, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 4, "column": 6}}, {"generated": {"line": 4, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 4, "column": 7}, "name": "sex"}, {"generated": {"line": 4, "column": 10}, "source": "src/chinese_people.js", "original": {"line": 4, "column": 10}}, {"generated": {"line": 4, "column": 13}, "source": "src/chinese_people.js", "original": {"line": 4, "column": 13}, "name": "sex"}, {"generated": {"line": 4, "column": 16}, "source": "src/chinese_people.js", "original": {"line": 4, "column": 16}}, {"generated": {"line": 5, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 5, "column": 2}}, {"generated": {"line": 5, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 5, "column": 6}}, {"generated": {"line": 5, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 5, "column": 7}, "name": "phone"}, {"generated": {"line": 5, "column": 12}, "source": "src/chinese_people.js", "original": {"line": 5, "column": 12}}, {"generated": {"line": 5, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 5, "column": 15}, "name": "phone"}, {"generated": {"line": 5, "column": 20}, "source": "src/chinese_people.js", "original": {"line": 5, "column": 20}}, {"generated": {"line": 6, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 6, "column": 2}}, {"generated": {"line": 6, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 6, "column": 6}}, {"generated": {"line": 6, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 6, "column": 7}, "name": "say"}, {"generated": {"line": 6, "column": 10}, "source": "src/chinese_people.js", "original": {"line": 6, "column": 10}}, {"generated": {"line": 6, "column": 13}, "source": "src/chinese_people.js", "original": {"line": 6, "column": 13}}, {"generated": {"line": 6, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 6, "column": 25}}, {"generated": {"line": 7, "column": 4}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 4}, "name": "console"}, {"generated": {"line": 7, "column": 11}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 11}}, {"generated": {"line": 7, "column": 12}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 12}, "name": "log"}, {"generated": {"line": 7, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 15}}, {"generated": {"line": 7, "column": 16}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 16}}, {"generated": {"line": 7, "column": 24}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 24}}, {"generated": {"line": 7, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 7, "column": 25}}, {"generated": {"line": 8, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 8, "column": 2}}, {"generated": {"line": 8, "column": 3}, "source": "src/chinese_people.js", "original": {"line": 8, "column": 3}}, {"generated": {"line": 9, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 9, "column": 0}}, {"generated": {"line": 10, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 0}, "name": "People"}, {"generated": {"line": 10, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 6}}, {"generated": {"line": 10, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 7}, "name": "prototype"}, {"generated": {"line": 10, "column": 16}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 16}}, {"generated": {"line": 10, "column": 17}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 17}, "name": "doEat"}, {"generated": {"line": 10, "column": 22}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 22}}, {"generated": {"line": 10, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 25}}, {"generated": {"line": 10, "column": 37}, "source": "src/chinese_people.js", "original": {"line": 10, "column": 37}}, {"generated": {"line": 11, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 11, "column": 2}}, {"generated": {"line": 12, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 2}, "name": "console"}, {"generated": {"line": 12, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 9}}, {"generated": {"line": 12, "column": 10}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 10}, "name": "log"}, {"generated": {"line": 12, "column": 13}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 13}}, {"generated": {"line": 12, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 14}}, {"generated": {"line": 12, "column": 18}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 18}}, {"generated": {"line": 12, "column": 19}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 19}, "name": "name"}, {"generated": {"line": 12, "column": 23}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 23}}, {"generated": {"line": 12, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 26}}, {"generated": {"line": 12, "column": 33}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 33}}, {"generated": {"line": 12, "column": 34}, "source": "src/chinese_people.js", "original": {"line": 12, "column": 34}}, {"generated": {"line": 13, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 13, "column": 0}}, {"generated": {"line": 13, "column": 1}, "source": "src/chinese_people.js", "original": {"line": 13, "column": 1}}, {"generated": {"line": 14, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 0}}, {"generated": {"line": 14, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 9}, "name": "ChinesePeople"}, {"generated": {"line": 14, "column": 22}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 22}, "name": "ChinesePeople"}, {"generated": {"line": 14, "column": 23}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 23}, "name": "name"}, {"generated": {"line": 14, "column": 27}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 27}}, {"generated": {"line": 14, "column": 29}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 29}, "name": "sex"}, {"generated": {"line": 14, "column": 32}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 32}}, {"generated": {"line": 14, "column": 34}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 34}, "name": "phone"}, {"generated": {"line": 14, "column": 39}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 39}}, {"generated": {"line": 14, "column": 41}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 41}, "name": "language"}, {"generated": {"line": 14, "column": 49}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 49}}, {"generated": {"line": 14, "column": 51}, "source": "src/chinese_people.js", "original": {"line": 14, "column": 51}}, {"generated": {"line": 15, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 15, "column": 2}}, {"generated": {"line": 16, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 2}, "name": "People"}, {"generated": {"line": 16, "column": 8}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 8}}, {"generated": {"line": 16, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 9}, "name": "apply"}, {"generated": {"line": 16, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 14}}, {"generated": {"line": 16, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 15}}, {"generated": {"line": 16, "column": 19}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 19}}, {"generated": {"line": 16, "column": 21}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 21}}, {"generated": {"line": 16, "column": 22}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 22}, "name": "name"}, {"generated": {"line": 16, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 26}}, {"generated": {"line": 16, "column": 28}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 28}, "name": "sex"}, {"generated": {"line": 16, "column": 31}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 31}}, {"generated": {"line": 16, "column": 33}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 33}, "name": "phone"}, {"generated": {"line": 16, "column": 38}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 38}}, {"generated": {"line": 16, "column": 39}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 39}}, {"generated": {"line": 16, "column": 40}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 40}}, {"generated": {"line": 16, "column": 41}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 41}}, {"generated": {"line": 16, "column": 42}, "source": "src/chinese_people.js", "original": {"line": 16, "column": 42}}, {"generated": {"line": 17, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 17, "column": 2}}, {"generated": {"line": 17, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 17, "column": 6}}, {"generated": {"line": 17, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 17, "column": 7}, "name": "language"}, {"generated": {"line": 17, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 17, "column": 15}}, {"generated": {"line": 17, "column": 18}, "source": "src/chinese_people.js", "original": {"line": 17, "column": 18}, "name": "language"}, {"generated": {"line": 17, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 17, "column": 26}}, {"generated": {"line": 18, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 18, "column": 0}}, {"generated": {"line": 19, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 19, "column": 0}}, {"generated": {"line": 21, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 21, "column": 0}}, {"generated": {"line": 22, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 0}}, {"generated": {"line": 22, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 9}, "name": "createNewPrototypeObj"}, {"generated": {"line": 22, "column": 30}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 30}, "name": "createNewPrototypeObj"}, {"generated": {"line": 22, "column": 31}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 31}, "name": "root"}, {"generated": {"line": 22, "column": 35}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 35}}, {"generated": {"line": 22, "column": 37}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 37}, "name": "son"}, {"generated": {"line": 22, "column": 40}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 40}}, {"generated": {"line": 22, "column": 42}, "source": "src/chinese_people.js", "original": {"line": 22, "column": 42}}, {"generated": {"line": 23, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 23, "column": 2}}, {"generated": {"line": 23, "column": 11}, "source": "src/chinese_people.js", "original": {"line": 23, "column": 11}, "name": "Middle"}, {"generated": {"line": 23, "column": 17}, "source": "src/chinese_people.js", "original": {"line": 23, "column": 17}, "name": "Middle"}, {"generated": {"line": 23, "column": 18}, "source": "src/chinese_people.js", "original": {"line": 23, "column": 17}}, {"generated": {"line": 23, "column": 20}, "source": "src/chinese_people.js", "original": {"line": 23, "column": 20}}, {"generated": {"line": 24, "column": 4}, "source": "src/chinese_people.js", "original": {"line": 24, "column": 4}}, {"generated": {"line": 24, "column": 8}, "source": "src/chinese_people.js", "original": {"line": 24, "column": 8}}, {"generated": {"line": 24, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 24, "column": 9}, "name": "count"}, {"generated": {"line": 24, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 24, "column": 14}}, {"generated": {"line": 24, "column": 17}, "source": "src/chinese_people.js", "original": {"line": 24, "column": 17}}, {"generated": {"line": 24, "column": 19}, "source": "src/chinese_people.js", "original": {"line": 24, "column": 19}}, {"generated": {"line": 25, "column": 4}, "source": "src/chinese_people.js", "original": {"line": 25, "column": 4}}, {"generated": {"line": 26, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 26, "column": 2}}, {"generated": {"line": 27, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 2}, "name": "Middle"}, {"generated": {"line": 27, "column": 8}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 8}}, {"generated": {"line": 27, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 9}, "name": "prototype"}, {"generated": {"line": 27, "column": 18}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 18}}, {"generated": {"line": 27, "column": 21}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 21}, "name": "root"}, {"generated": {"line": 27, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 25}}, {"generated": {"line": 27, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 26}, "name": "prototype"}, {"generated": {"line": 27, "column": 35}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 35}}, {"generated": {"line": 27, "column": 36}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 36}}, {"generated": {"line": 27, "column": 37}, "source": "src/chinese_people.js", "original": {"line": 28, "column": 37}}, {"generated": {"line": 29, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 2}}, {"generated": {"line": 29, "column": 6}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 6}, "name": "middle"}, {"generated": {"line": 29, "column": 12}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 12}}, {"generated": {"line": 29, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 15}}, {"generated": {"line": 29, "column": 19}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 19}, "name": "Middle"}, {"generated": {"line": 29, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 25}}, {"generated": {"line": 29, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 26}}, {"generated": {"line": 29, "column": 27}, "source": "src/chinese_people.js", "original": {"line": 30, "column": 27}}, {"generated": {"line": 30, "column": 2}, "source": "src/chinese_people.js", "original": {"line": 31, "column": 2}}, {"generated": {"line": 30, "column": 9}, "source": "src/chinese_people.js", "original": {"line": 31, "column": 9}, "name": "middle"}, {"generated": {"line": 30, "column": 15}, "source": "src/chinese_people.js", "original": {"line": 31, "column": 15}}, {"generated": {"line": 31, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 32, "column": 0}}, {"generated": {"line": 33, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 34, "column": 0}}, {"generated": {"line": 34, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 35, "column": 0}}, {"generated": {"line": 35, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 36, "column": 0}}, {"generated": {"line": 36, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 37, "column": 0}}, {"generated": {"line": 38, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 0}, "name": "ChinesePeople"}, {"generated": {"line": 38, "column": 13}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 13}}, {"generated": {"line": 38, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 14}, "name": "prototype"}, {"generated": {"line": 38, "column": 23}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 23}}, {"generated": {"line": 38, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 26}, "name": "createNewPrototypeObj"}, {"generated": {"line": 38, "column": 47}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 47}}, {"generated": {"line": 38, "column": 48}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 48}, "name": "People"}, {"generated": {"line": 38, "column": 54}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 54}}, {"generated": {"line": 38, "column": 56}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 56}, "name": "ChinesePeople"}, {"generated": {"line": 38, "column": 69}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 69}}, {"generated": {"line": 38, "column": 70}, "source": "src/chinese_people.js", "original": {"line": 39, "column": 70}}, {"generated": {"line": 39, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 0}, "name": "ChinesePeople"}, {"generated": {"line": 39, "column": 13}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 13}}, {"generated": {"line": 39, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 14}, "name": "prototype"}, {"generated": {"line": 39, "column": 23}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 23}}, {"generated": {"line": 39, "column": 24}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 24}, "name": "constructor"}, {"generated": {"line": 39, "column": 35}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 35}}, {"generated": {"line": 39, "column": 38}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 38}, "name": "ChinesePeople"}, {"generated": {"line": 39, "column": 51}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 51}}, {"generated": {"line": 39, "column": 52}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 52}}, {"generated": {"line": 39, "column": 53}, "source": "src/chinese_people.js", "original": {"line": 41, "column": 53}}, {"generated": {"line": 40, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 0}}, {"generated": {"line": 40, "column": 4}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 4}, "name": "chinesePeople"}, {"generated": {"line": 40, "column": 17}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 17}}, {"generated": {"line": 40, "column": 20}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 20}}, {"generated": {"line": 40, "column": 24}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 24}, "name": "ChinesePeople"}, {"generated": {"line": 40, "column": 37}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 37}}, {"generated": {"line": 40, "column": 38}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 38}}, {"generated": {"line": 40, "column": 42}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 42}}, {"generated": {"line": 40, "column": 44}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 44}}, {"generated": {"line": 40, "column": 47}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 47}}, {"generated": {"line": 40, "column": 49}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 49}}, {"generated": {"line": 40, "column": 62}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 62}}, {"generated": {"line": 40, "column": 64}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 64}}, {"generated": {"line": 40, "column": 68}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 68}}, {"generated": {"line": 40, "column": 69}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 69}}, {"generated": {"line": 40, "column": 70}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 70}}, {"generated": {"line": 40, "column": 71}, "source": "src/chinese_people.js", "original": {"line": 42, "column": 71}}, {"generated": {"line": 41, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 0}, "name": "console"}, {"generated": {"line": 41, "column": 7}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 7}}, {"generated": {"line": 41, "column": 8}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 8}, "name": "log"}, {"generated": {"line": 41, "column": 11}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 11}}, {"generated": {"line": 41, "column": 12}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 12}, "name": "chinesePeople"}, {"generated": {"line": 41, "column": 25}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 25}}, {"generated": {"line": 41, "column": 26}, "source": "src/chinese_people.js", "original": {"line": 43, "column": 26}}, {"generated": {"line": 42, "column": 0}, "source": "src/chinese_people.js", "original": {"line": 44, "column": 0}, "name": "chinesePeople"}, {"generated": {"line": 42, "column": 13}, "source": "src/chinese_people.js", "original": {"line": 44, "column": 13}}, {"generated": {"line": 42, "column": 14}, "source": "src/chinese_people.js", "original": {"line": 44, "column": 14}, "name": "doEat"}, {"generated": {"line": 42, "column": 19}, "source": "src/chinese_people.js", "original": {"line": 44, "column": 19}}, {"generated": {"line": 42, "column": 20}, "source": "src/chinese_people.js", "original": {"line": 44, "column": 20}}, {"generated": {"line": 42, "column": 21}, "source": "src/chinese_people.js", "original": {"line": 44, "column": 21}}], "sources": {"src/chinese_people.js": "function People(name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n  this.say = function () {\n    console.log(\"say...\");\n  };\n}\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\nfunction ChinesePeople(name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n}\n// ChinesePeople.prototype = Object.create(People.prototype); // 原型链继承 （new People() 有很多缺点， Object.create(People.prototype) ）\n\n// 构建一个公用的寄生组合继承函数【最佳原型继承】- 即是Object.creae()的实现\nfunction createNewPrototypeObj(root, son) {\n  function Middle() {\n    this.count = 23;\n    // this.constructor = son; // 等同 ChinesePeople.prototype.constructor = ChinesePeople\n  }\n\n  Middle.prototype = root.prototype; // People.prototype;\n\n  let middle = new Middle();\n  return middle;\n}\n\n// 寄生组合继承\n// function midle() {}\n// midle.prototype = People.prototype; // 约等同于  Object.create(People.prototype)\n// ChinesePeople.prototype = new midle();\n\nChinesePeople.prototype = createNewPrototypeObj(People, ChinesePeople);\n\nChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步\nlet chinesePeople = new ChinesePeople(\"张三\", \"男\", \"13888888888\", \"中文\"); // 子类实例\nconsole.log(chinesePeople);\nchinesePeople.doEat();\n"}, "lineCount": null}}, "error": null, "hash": "527578c36627670de4fe4fab851a4c9c", "cacheData": {"env": {}}}