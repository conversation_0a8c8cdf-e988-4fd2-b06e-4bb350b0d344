{"id": "src/mysetprototypeof.js", "dependencies": [{"name": "/Users/<USER>/Documents/workspaces/ts_workspaces/moke_ts/05/package.json", "includedInParent": true, "mtime": 1748261705881}], "generated": {"js": "function _extendsv1(parent) {\n  var middle = Object.create(parent.prototype, {\n    count: {\n      writable: true,\n      value: 23\n    }\n  }); // 中间对象\n  return middle;\n}\nfunction _extendsv2(parent) {\n  var middle = {\n    count: 23\n  }; // 中间对象\n\n  //   setPrototypeOf 方法：修改一个对象的原型对象，这里是修改 middle 的原型对象为 parent.prototype\n  //   let newmiddle = Object.setPrototypeOf(middle, parent.prototype);\n  //   console.log(\"_extneds=>middle\", middle);\n  //   console.log(\"_extneds=>newmiddle\", newmiddle);\n  //   //   middle.__proto__ = parent.prototype; // 原型链继承;\n  //   return newmiddle;\n\n  // 简化版\n  return Object.setPrototypeOf(middle, parent.prototype);\n}\nvar middle = _extendsv2(People);\nfunction People(name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n}\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\nfunction ChinesePeople(name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n}\nChinesePeople.prototype.drink = function () {\n  console.log(this.name + \"喝水...\");\n};\nChinesePeople.prototype = middle;\nChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步\nvar chinesePeople1 = new ChinesePeople(\"张三\", \"男\", \"13888888888\", \"中文\");\nconsole.log(chinesePeople1);\nconsole.log(chinesePeople1 instanceof People); // true\nconsole.log(chinesePeople1 instanceof ChinesePeople); // true\n\n// TypeError: chinesePeople1.drink is not a function 错误\n// 原因：ChinesePeople.prototype = middle; 这行代码将 ChinesePeople 的原型指向了 middle，而 middle 并没有 drink 方法。\n// 这里setPrototypeOf方法修改了构造函数的原型对象，所以之前定义的方法被覆盖了，解决方法就是通过原型链的继承来解决，即chinesePeople1.__proto__还是指向ChinesePeople.prototype，ChinesePeople.prototype.__proto__指向People.prototype。\n// 这样chinesePeople1就可以访问自己原型对象空间中的方法，也可以访问父类原型对象空间中的方法。在mysetprototypeof2.js中已经解决了这个问题。\nchinesePeople1.drink();"}, "sourceMaps": {"js": {"mappings": [{"generated": {"line": 1, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 1, "column": 0}}, {"generated": {"line": 1, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 1, "column": 9}, "name": "_extendsv1"}, {"generated": {"line": 1, "column": 19}, "source": "src/mysetprototypeof.js", "original": {"line": 1, "column": 19}, "name": "_extendsv1"}, {"generated": {"line": 1, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 1, "column": 20}, "name": "parent"}, {"generated": {"line": 1, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 1, "column": 26}}, {"generated": {"line": 1, "column": 28}, "source": "src/mysetprototypeof.js", "original": {"line": 1, "column": 28}}, {"generated": {"line": 2, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 2}}, {"generated": {"line": 2, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 6}, "name": "middle"}, {"generated": {"line": 2, "column": 12}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 12}}, {"generated": {"line": 2, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 15}, "name": "Object"}, {"generated": {"line": 2, "column": 21}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 21}}, {"generated": {"line": 2, "column": 22}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 22}, "name": "create"}, {"generated": {"line": 2, "column": 28}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 28}}, {"generated": {"line": 2, "column": 29}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 29}, "name": "parent"}, {"generated": {"line": 2, "column": 35}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 35}}, {"generated": {"line": 2, "column": 36}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 36}, "name": "prototype"}, {"generated": {"line": 2, "column": 45}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 45}}, {"generated": {"line": 2, "column": 47}, "source": "src/mysetprototypeof.js", "original": {"line": 2, "column": 47}}, {"generated": {"line": 3, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 3, "column": 4}, "name": "count"}, {"generated": {"line": 3, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 3, "column": 9}}, {"generated": {"line": 3, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 3, "column": 11}}, {"generated": {"line": 4, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 4, "column": 6}, "name": "writable"}, {"generated": {"line": 4, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 4, "column": 14}}, {"generated": {"line": 4, "column": 16}, "source": "src/mysetprototypeof.js", "original": {"line": 4, "column": 16}}, {"generated": {"line": 4, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 4, "column": 20}}, {"generated": {"line": 5, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 5, "column": 6}, "name": "value"}, {"generated": {"line": 5, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 5, "column": 11}}, {"generated": {"line": 5, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 5, "column": 13}}, {"generated": {"line": 6, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 6, "column": 4}}, {"generated": {"line": 7, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 7, "column": 2}}, {"generated": {"line": 7, "column": 3}, "source": "src/mysetprototypeof.js", "original": {"line": 7, "column": 3}}, {"generated": {"line": 7, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 7, "column": 4}}, {"generated": {"line": 7, "column": 5}, "source": "src/mysetprototypeof.js", "original": {"line": 7, "column": 5}}, {"generated": {"line": 7, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 7, "column": 6}}, {"generated": {"line": 8, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 8, "column": 2}}, {"generated": {"line": 8, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 8, "column": 9}, "name": "middle"}, {"generated": {"line": 8, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 8, "column": 15}}, {"generated": {"line": 9, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 9, "column": 0}}, {"generated": {"line": 10, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 11, "column": 0}}, {"generated": {"line": 10, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 11, "column": 9}, "name": "_extendsv2"}, {"generated": {"line": 10, "column": 19}, "source": "src/mysetprototypeof.js", "original": {"line": 11, "column": 19}, "name": "_extendsv2"}, {"generated": {"line": 10, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 11, "column": 20}, "name": "parent"}, {"generated": {"line": 10, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 11, "column": 26}}, {"generated": {"line": 10, "column": 28}, "source": "src/mysetprototypeof.js", "original": {"line": 11, "column": 28}}, {"generated": {"line": 11, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 2}}, {"generated": {"line": 11, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 6}, "name": "middle"}, {"generated": {"line": 11, "column": 12}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 12}}, {"generated": {"line": 11, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 15}}, {"generated": {"line": 12, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 17}, "name": "count"}, {"generated": {"line": 12, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 22}}, {"generated": {"line": 12, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 24}}, {"generated": {"line": 13, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 27}}, {"generated": {"line": 13, "column": 3}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 28}}, {"generated": {"line": 13, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 29}}, {"generated": {"line": 13, "column": 5}, "source": "src/mysetprototypeof.js", "original": {"line": 12, "column": 30}}, {"generated": {"line": 15, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 14, "column": 2}}, {"generated": {"line": 16, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 15, "column": 2}}, {"generated": {"line": 17, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 16, "column": 2}}, {"generated": {"line": 18, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 17, "column": 2}}, {"generated": {"line": 19, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 18, "column": 2}}, {"generated": {"line": 20, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 19, "column": 2}}, {"generated": {"line": 22, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 21, "column": 2}}, {"generated": {"line": 23, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 2}}, {"generated": {"line": 23, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 9}, "name": "Object"}, {"generated": {"line": 23, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 15}}, {"generated": {"line": 23, "column": 16}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 16}, "name": "setPrototypeOf"}, {"generated": {"line": 23, "column": 30}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 30}}, {"generated": {"line": 23, "column": 31}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 31}, "name": "middle"}, {"generated": {"line": 23, "column": 37}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 37}}, {"generated": {"line": 23, "column": 39}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 39}, "name": "parent"}, {"generated": {"line": 23, "column": 45}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 45}}, {"generated": {"line": 23, "column": 46}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 46}, "name": "prototype"}, {"generated": {"line": 23, "column": 55}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 55}}, {"generated": {"line": 23, "column": 56}, "source": "src/mysetprototypeof.js", "original": {"line": 22, "column": 56}}, {"generated": {"line": 24, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 23, "column": 0}}, {"generated": {"line": 25, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 0}}, {"generated": {"line": 25, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 4}, "name": "middle"}, {"generated": {"line": 25, "column": 10}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 10}}, {"generated": {"line": 25, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 13}, "name": "_extendsv2"}, {"generated": {"line": 25, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 23}}, {"generated": {"line": 25, "column": 24}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 24}, "name": "People"}, {"generated": {"line": 25, "column": 30}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 30}}, {"generated": {"line": 25, "column": 31}, "source": "src/mysetprototypeof.js", "original": {"line": 25, "column": 31}}, {"generated": {"line": 26, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 0}}, {"generated": {"line": 26, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 9}, "name": "People"}, {"generated": {"line": 26, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 15}, "name": "People"}, {"generated": {"line": 26, "column": 16}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 16}, "name": "name"}, {"generated": {"line": 26, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 20}}, {"generated": {"line": 26, "column": 22}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 22}, "name": "sex"}, {"generated": {"line": 26, "column": 25}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 25}}, {"generated": {"line": 26, "column": 27}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 27}, "name": "phone"}, {"generated": {"line": 26, "column": 32}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 32}}, {"generated": {"line": 26, "column": 34}, "source": "src/mysetprototypeof.js", "original": {"line": 27, "column": 34}}, {"generated": {"line": 27, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 28, "column": 2}}, {"generated": {"line": 28, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 2}}, {"generated": {"line": 28, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 6}}, {"generated": {"line": 28, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 7}, "name": "name"}, {"generated": {"line": 28, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 11}}, {"generated": {"line": 28, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 14}, "name": "name"}, {"generated": {"line": 28, "column": 18}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 18}}, {"generated": {"line": 28, "column": 19}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 19}}, {"generated": {"line": 28, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 29, "column": 20}}, {"generated": {"line": 29, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 30, "column": 2}}, {"generated": {"line": 29, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 30, "column": 6}}, {"generated": {"line": 29, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 30, "column": 7}, "name": "sex"}, {"generated": {"line": 29, "column": 10}, "source": "src/mysetprototypeof.js", "original": {"line": 30, "column": 10}}, {"generated": {"line": 29, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 30, "column": 13}, "name": "sex"}, {"generated": {"line": 29, "column": 16}, "source": "src/mysetprototypeof.js", "original": {"line": 30, "column": 16}}, {"generated": {"line": 30, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 31, "column": 2}}, {"generated": {"line": 30, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 31, "column": 6}}, {"generated": {"line": 30, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 31, "column": 7}, "name": "phone"}, {"generated": {"line": 30, "column": 12}, "source": "src/mysetprototypeof.js", "original": {"line": 31, "column": 12}}, {"generated": {"line": 30, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 31, "column": 15}, "name": "phone"}, {"generated": {"line": 30, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 31, "column": 20}}, {"generated": {"line": 31, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 32, "column": 0}}, {"generated": {"line": 32, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 0}, "name": "People"}, {"generated": {"line": 32, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 6}}, {"generated": {"line": 32, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 7}, "name": "prototype"}, {"generated": {"line": 32, "column": 16}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 16}}, {"generated": {"line": 32, "column": 17}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 17}, "name": "doEat"}, {"generated": {"line": 32, "column": 22}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 22}}, {"generated": {"line": 32, "column": 25}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 25}}, {"generated": {"line": 32, "column": 37}, "source": "src/mysetprototypeof.js", "original": {"line": 34, "column": 37}}, {"generated": {"line": 33, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 35, "column": 2}}, {"generated": {"line": 34, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 2}, "name": "console"}, {"generated": {"line": 34, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 9}}, {"generated": {"line": 34, "column": 10}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 10}, "name": "log"}, {"generated": {"line": 34, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 13}}, {"generated": {"line": 34, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 14}}, {"generated": {"line": 34, "column": 18}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 18}}, {"generated": {"line": 34, "column": 19}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 19}, "name": "name"}, {"generated": {"line": 34, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 23}}, {"generated": {"line": 34, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 26}}, {"generated": {"line": 34, "column": 33}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 33}}, {"generated": {"line": 34, "column": 34}, "source": "src/mysetprototypeof.js", "original": {"line": 36, "column": 34}}, {"generated": {"line": 35, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 37, "column": 0}}, {"generated": {"line": 35, "column": 1}, "source": "src/mysetprototypeof.js", "original": {"line": 37, "column": 1}}, {"generated": {"line": 36, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 0}}, {"generated": {"line": 36, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 9}, "name": "ChinesePeople"}, {"generated": {"line": 36, "column": 22}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 22}, "name": "ChinesePeople"}, {"generated": {"line": 36, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 23}, "name": "name"}, {"generated": {"line": 36, "column": 27}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 27}}, {"generated": {"line": 36, "column": 29}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 29}, "name": "sex"}, {"generated": {"line": 36, "column": 32}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 32}}, {"generated": {"line": 36, "column": 34}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 34}, "name": "phone"}, {"generated": {"line": 36, "column": 39}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 39}}, {"generated": {"line": 36, "column": 41}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 41}, "name": "language"}, {"generated": {"line": 36, "column": 49}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 49}}, {"generated": {"line": 36, "column": 51}, "source": "src/mysetprototypeof.js", "original": {"line": 39, "column": 51}}, {"generated": {"line": 37, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 40, "column": 2}}, {"generated": {"line": 38, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 2}, "name": "People"}, {"generated": {"line": 38, "column": 8}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 8}}, {"generated": {"line": 38, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 9}, "name": "apply"}, {"generated": {"line": 38, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 14}}, {"generated": {"line": 38, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 15}}, {"generated": {"line": 38, "column": 19}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 19}}, {"generated": {"line": 38, "column": 21}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 21}}, {"generated": {"line": 38, "column": 22}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 22}, "name": "name"}, {"generated": {"line": 38, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 26}}, {"generated": {"line": 38, "column": 28}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 28}, "name": "sex"}, {"generated": {"line": 38, "column": 31}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 31}}, {"generated": {"line": 38, "column": 33}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 33}, "name": "phone"}, {"generated": {"line": 38, "column": 38}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 38}}, {"generated": {"line": 38, "column": 39}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 39}}, {"generated": {"line": 38, "column": 40}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 40}}, {"generated": {"line": 38, "column": 41}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 41}}, {"generated": {"line": 38, "column": 42}, "source": "src/mysetprototypeof.js", "original": {"line": 41, "column": 42}}, {"generated": {"line": 39, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 42, "column": 2}}, {"generated": {"line": 39, "column": 6}, "source": "src/mysetprototypeof.js", "original": {"line": 42, "column": 6}}, {"generated": {"line": 39, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 42, "column": 7}, "name": "language"}, {"generated": {"line": 39, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 42, "column": 15}}, {"generated": {"line": 39, "column": 18}, "source": "src/mysetprototypeof.js", "original": {"line": 42, "column": 18}, "name": "language"}, {"generated": {"line": 39, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 42, "column": 26}}, {"generated": {"line": 40, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 43, "column": 0}}, {"generated": {"line": 41, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 0}, "name": "ChinesePeople"}, {"generated": {"line": 41, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 13}}, {"generated": {"line": 41, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 14}, "name": "prototype"}, {"generated": {"line": 41, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 23}}, {"generated": {"line": 41, "column": 24}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 24}, "name": "drink"}, {"generated": {"line": 41, "column": 29}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 29}}, {"generated": {"line": 41, "column": 32}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 32}}, {"generated": {"line": 41, "column": 44}, "source": "src/mysetprototypeof.js", "original": {"line": 45, "column": 44}}, {"generated": {"line": 42, "column": 2}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 2}, "name": "console"}, {"generated": {"line": 42, "column": 9}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 9}}, {"generated": {"line": 42, "column": 10}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 10}, "name": "log"}, {"generated": {"line": 42, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 13}}, {"generated": {"line": 42, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 14}}, {"generated": {"line": 42, "column": 18}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 18}}, {"generated": {"line": 42, "column": 19}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 19}, "name": "name"}, {"generated": {"line": 42, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 23}}, {"generated": {"line": 42, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 26}}, {"generated": {"line": 42, "column": 33}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 33}}, {"generated": {"line": 42, "column": 34}, "source": "src/mysetprototypeof.js", "original": {"line": 46, "column": 34}}, {"generated": {"line": 43, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 47, "column": 0}}, {"generated": {"line": 43, "column": 1}, "source": "src/mysetprototypeof.js", "original": {"line": 47, "column": 1}}, {"generated": {"line": 44, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 49, "column": 0}, "name": "ChinesePeople"}, {"generated": {"line": 44, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 49, "column": 13}}, {"generated": {"line": 44, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 49, "column": 14}, "name": "prototype"}, {"generated": {"line": 44, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 49, "column": 23}}, {"generated": {"line": 44, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 49, "column": 26}, "name": "middle"}, {"generated": {"line": 44, "column": 32}, "source": "src/mysetprototypeof.js", "original": {"line": 49, "column": 32}}, {"generated": {"line": 45, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 0}, "name": "ChinesePeople"}, {"generated": {"line": 45, "column": 13}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 13}}, {"generated": {"line": 45, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 14}, "name": "prototype"}, {"generated": {"line": 45, "column": 23}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 23}}, {"generated": {"line": 45, "column": 24}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 24}, "name": "constructor"}, {"generated": {"line": 45, "column": 35}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 35}}, {"generated": {"line": 45, "column": 38}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 38}, "name": "ChinesePeople"}, {"generated": {"line": 45, "column": 51}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 51}}, {"generated": {"line": 45, "column": 52}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 52}}, {"generated": {"line": 45, "column": 53}, "source": "src/mysetprototypeof.js", "original": {"line": 50, "column": 53}}, {"generated": {"line": 46, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 0}}, {"generated": {"line": 46, "column": 4}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 4}, "name": "chinesePeople1"}, {"generated": {"line": 46, "column": 18}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 18}}, {"generated": {"line": 46, "column": 21}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 21}}, {"generated": {"line": 46, "column": 25}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 25}, "name": "ChinesePeople"}, {"generated": {"line": 46, "column": 38}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 38}}, {"generated": {"line": 46, "column": 39}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 39}}, {"generated": {"line": 46, "column": 43}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 43}}, {"generated": {"line": 46, "column": 45}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 45}}, {"generated": {"line": 46, "column": 48}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 48}}, {"generated": {"line": 46, "column": 50}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 50}}, {"generated": {"line": 46, "column": 63}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 63}}, {"generated": {"line": 46, "column": 65}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 65}}, {"generated": {"line": 46, "column": 69}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 69}}, {"generated": {"line": 46, "column": 70}, "source": "src/mysetprototypeof.js", "original": {"line": 51, "column": 70}}, {"generated": {"line": 47, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 0}, "name": "console"}, {"generated": {"line": 47, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 7}}, {"generated": {"line": 47, "column": 8}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 8}, "name": "log"}, {"generated": {"line": 47, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 11}}, {"generated": {"line": 47, "column": 12}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 12}, "name": "chinesePeople1"}, {"generated": {"line": 47, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 26}}, {"generated": {"line": 47, "column": 27}, "source": "src/mysetprototypeof.js", "original": {"line": 52, "column": 27}}, {"generated": {"line": 48, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 0}, "name": "console"}, {"generated": {"line": 48, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 7}}, {"generated": {"line": 48, "column": 8}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 8}, "name": "log"}, {"generated": {"line": 48, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 11}}, {"generated": {"line": 48, "column": 12}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 12}, "name": "chinesePeople1"}, {"generated": {"line": 48, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 26}}, {"generated": {"line": 48, "column": 38}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 38}, "name": "People"}, {"generated": {"line": 48, "column": 44}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 44}}, {"generated": {"line": 48, "column": 45}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 45}}, {"generated": {"line": 48, "column": 46}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 46}}, {"generated": {"line": 48, "column": 47}, "source": "src/mysetprototypeof.js", "original": {"line": 53, "column": 47}}, {"generated": {"line": 49, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 0}, "name": "console"}, {"generated": {"line": 49, "column": 7}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 7}}, {"generated": {"line": 49, "column": 8}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 8}, "name": "log"}, {"generated": {"line": 49, "column": 11}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 11}}, {"generated": {"line": 49, "column": 12}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 12}, "name": "chinesePeople1"}, {"generated": {"line": 49, "column": 26}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 26}}, {"generated": {"line": 49, "column": 38}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 38}, "name": "ChinesePeople"}, {"generated": {"line": 49, "column": 51}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 51}}, {"generated": {"line": 49, "column": 52}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 52}}, {"generated": {"line": 49, "column": 53}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 53}}, {"generated": {"line": 49, "column": 54}, "source": "src/mysetprototypeof.js", "original": {"line": 54, "column": 54}}, {"generated": {"line": 51, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 56, "column": 0}}, {"generated": {"line": 52, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 57, "column": 0}}, {"generated": {"line": 53, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 58, "column": 0}}, {"generated": {"line": 54, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 59, "column": 0}}, {"generated": {"line": 55, "column": 0}, "source": "src/mysetprototypeof.js", "original": {"line": 60, "column": 0}, "name": "chinesePeople1"}, {"generated": {"line": 55, "column": 14}, "source": "src/mysetprototypeof.js", "original": {"line": 60, "column": 14}}, {"generated": {"line": 55, "column": 15}, "source": "src/mysetprototypeof.js", "original": {"line": 60, "column": 15}, "name": "drink"}, {"generated": {"line": 55, "column": 20}, "source": "src/mysetprototypeof.js", "original": {"line": 60, "column": 20}}, {"generated": {"line": 55, "column": 21}, "source": "src/mysetprototypeof.js", "original": {"line": 60, "column": 21}}, {"generated": {"line": 55, "column": 22}, "source": "src/mysetprototypeof.js", "original": {"line": 60, "column": 22}}], "sources": {"src/mysetprototypeof.js": "function _extendsv1(parent) {\n  let middle = Object.create(parent.prototype, {\n    count: {\n      writable: true,\n      value: 23,\n    },\n  }); // 中间对象\n  return middle;\n}\n\nfunction _extendsv2(parent) {\n  let middle = { count: 23 }; // 中间对象\n\n  //   setPrototypeOf 方法：修改一个对象的原型对象，这里是修改 middle 的原型对象为 parent.prototype\n  //   let newmiddle = Object.setPrototypeOf(middle, parent.prototype);\n  //   console.log(\"_extneds=>middle\", middle);\n  //   console.log(\"_extneds=>newmiddle\", newmiddle);\n  //   //   middle.__proto__ = parent.prototype; // 原型链继承;\n  //   return newmiddle;\n\n  // 简化版\n  return Object.setPrototypeOf(middle, parent.prototype);\n}\n\nlet middle = _extendsv2(People);\n\nfunction People(name, sex, phone) {\n  // People父构造函数【父类】\n  this.name = name; // 实例属性\n  this.sex = sex;\n  this.phone = phone;\n}\n\nPeople.prototype.doEat = function () {\n  // 原型方法\n  console.log(this.name + \"吃饭...\");\n};\n\nfunction ChinesePeople(name, sex, phone, language) {\n  // 子类\n  People.apply(this, [name, sex, phone]); // 借用父构造函数继承\n  this.language = language;\n}\n\nChinesePeople.prototype.drink = function () {\n  console.log(this.name + \"喝水...\");\n};\n\nChinesePeople.prototype = middle;\nChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步\nlet chinesePeople1 = new ChinesePeople(\"张三\", \"男\", \"13888888888\", \"中文\");\nconsole.log(chinesePeople1);\nconsole.log(chinesePeople1 instanceof People); // true\nconsole.log(chinesePeople1 instanceof ChinesePeople); // true\n\n// TypeError: chinesePeople1.drink is not a function 错误\n// 原因：ChinesePeople.prototype = middle; 这行代码将 ChinesePeople 的原型指向了 middle，而 middle 并没有 drink 方法。\n// 这里setPrototypeOf方法修改了构造函数的原型对象，所以之前定义的方法被覆盖了，解决方法就是通过原型链的继承来解决，即chinesePeople1.__proto__还是指向ChinesePeople.prototype，ChinesePeople.prototype.__proto__指向People.prototype。\n// 这样chinesePeople1就可以访问自己原型对象空间中的方法，也可以访问父类原型对象空间中的方法。在mysetprototypeof2.js中已经解决了这个问题。\nchinesePeople1.drink();\n"}, "lineCount": null}}, "error": null, "hash": "1372a853d8b94e54be53fadf2334c56a", "cacheData": {"env": {}}}