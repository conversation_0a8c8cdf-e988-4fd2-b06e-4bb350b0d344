Promise.resolve().then(() => {
  console.log(1);
});

setTimeout(() => console.log(2), 10);

queueMicrotask(() => {
  console.log(3);

  queueMicrotask(() => console.log(4));
});

console.log(5);

// output 5 1 3 4 2
// 原因：
// 1. console.log(5) 是同步任务，直接执行
// 2. Promise.resolve().then(() => console.log(1)) 是微任务，进入微任务队列
// 3. setTimeout(() => console.log(2), 10) 是宏任务，进入宏任务队列
// 4. queueMicrotask(() => console.log(3)) 是微任务，进入微任务队列
// 5. queueMicrotask(() => console.log(4)) 是微任务，进入微任务队列
// 6. 微任务队列中的任务先执行，所以先输出 1 3 4
// 7. 宏任务队列中的任务后执行，所以最后输出 2
