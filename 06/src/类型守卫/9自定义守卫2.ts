class StringUtils {
  // 自定义类型守卫： 判断是否是字符串
  static isString(value: any): value is string {
    return typeof value === "string";
  }
  static trim(str: string) {
    return str.replace(/\s+/g, "");
  }

  // 自定义类型守卫： 判断是否是函数
  static isFunction(value: any): value is Function {
    return typeof value === "function";
  }
}

interface TestInter {
  username: string;
  age: number;
  eat(): void;
  allowoutput?: 1;
}

let testObj: TestInter = {
  username: "  123  ",
  age: 18,
  eat() {
    console.log("eat");
  },
  allowoutput: 1,
};

function processObjOutput(obj: any) {
  Object.keys(obj).forEach((key) => {
    if (StringUtils.isString(obj[key])) {
      console.log(StringUtils.trim(obj[key]));
    } else if (StringUtils.isFunction(obj[key])) {
      obj[key]();
    } else {
      console.log(key, obj[key]);
    }
  });
}

processObjOutput(testObj);

export {};
