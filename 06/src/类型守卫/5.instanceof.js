// instanceof 格式： 对象变量 instanceof 类名或函数名
// instanceof的主要作用 :  instanceof 帮助我们准确的判断一种自定义函数或类创建的对象的变量的数据类型。

// instanceof 执行后返回 true的几种条件【符合一个即可】
// 对象变量.__proto_ = 类名或函数名.prototype
// 对象变量.proto.proto.....__proto__ = 类名或函数名.prototype

function People(name, sex, phone) {
  // People父构造函数【父类】
  this.name = name; // 实例属性
  this.sex = sex;
  this.phone = phone;
}

People.prototype.doEat = function () {
  // 原型方法
  console.log(this.name + "吃饭...");
};

function ChinesePeople(name, sex, phone, language) {
  // 子类
  People.apply(this, [name, sex, phone]); // 借用父构造函数继承
  this.language = language;
}

function Middle() {}

Middle.prototype = People.prototype;
ChinesePeople.prototype = new Middle();

console.log("-----", new Middle().constructor);
console.log(Middle.prototype.constructor);

ChinesePeople.prototype.constructor = ChinesePeople; // 原型链继承的重要一步
let chinesePeople1 = new ChinesePeople("张三", "男", "13888888888", "中文");
console.log(chinesePeople1);
console.log(chinesePeople1 instanceof People); // true

if (chinesePeople1 instanceof ChinesePeople) {
  console.log("chinesePeople1 instanceof ChinesePeople => true");
}
console.log(chinesePeople1.__proto__ === ChinesePeople.prototype);

if (chinesePeople1 instanceof People) {
  console.log("chinesePeople1 instanceof People => true");
}
console.log(chinesePeople1.__proto__.__proto__ === People.prototype);

if (chinesePeople1 instanceof Object) {
  console.log("chinesePeople1 instanceof Object => true");
}
console.log(chinesePeople1.__proto__.__proto__.__proto__ === Object.prototype);
