// typeof作用：用来检测一个变量或者一个对象的数据类型

// typeof 检测范围： number string boolean undefined symbol bigInt object function
// 不能检测null
// 不能检测引用数据类型的具体类型
// 不能检测函数的具体类型
// 不能检测是否是数组

const People = {
  name: "张三",
  age: 18,
};

console.log(typeof People); // object

function sayHello() {
  console.log("hello");
}

console.log(typeof sayHello); // function

const ages = [1, 2, 3];
console.log(typeof ages); // object

const a = null;
console.log(typeof a); // object

class StringUtil {
  public static trimpSpace(str: string): string {
    return str.replace(/\s+/g, "");
  }
}

let su = new StringUtil();
console.log(typeof su); // object

typeof new StringUtil(); // object

let x = "3";
typeof x;

let arr = new Array([]);
typeof arr; // object

console.log(" typeof arr: ", typeof arr);
console.log("type of new Array([])", typeof new Array([]));
console.log("typeof []:", typeof []);

// typeof 的替代方案
Object.prototype.toString.call(1); // [object Number]
Object.prototype.toString.call("1"); // [object String]
Object.prototype.toString.call(true); // [object Boolean]
Object.prototype.toString.call(undefined); // [object Undefined]
Object.prototype.toString.call(null); // [object Null]
Object.prototype.toString.call(Symbol()); // [object Symbol]
Object.prototype.toString.call(new Date()); // [object Date]
Object.prototype.toString.call([]); // [object Array]
Object.prototype.toString.call({}); // [object Object]
Object.prototype.toString.call(function () {}); // [object Function]

console.log(Object.prototype.toString.call(1));

// typeof的替代方法依然无法解决的问题
// 就是无法获取一个自定义的类的实例变量或构造函数的的对象变量的真正创建类型，答案是使用instanceof来解决
