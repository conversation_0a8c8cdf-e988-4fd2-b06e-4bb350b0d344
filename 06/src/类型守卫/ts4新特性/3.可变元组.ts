// 1. 泛型中的扩展运算符
// 在 TypeScript 4.0 之前，
// 元组类型中的扩展运算符（...）只能用于数组类型，
// 并且必须放在元组的最后。TypeScript 4.0 放宽了这一限制，
// 允许在泛型中使用扩展运算符，并且剩余元素可以出现在元组的任意位置。

function tail<T extends any[]>(arr: readonly [any, ...T]): T {
  // _ignored 解构后的第一个元素
  const [_ignored, ...rest] = arr;
  // 返回剩余的元素
  return rest;
}

const myTuple = [1, 2, 3, 4] as const;
const myArray = ["hello", "world"] as const;

const r1 = tail(myTuple); // [2, 3, 4]
console.log(r1);

const r2 = tail(myArray); // ["world"]
console.log(r2);

const r3 = tail([...myTuple, ...myArray]); // [2, 3, 4, "world"]
console.log(r3);

export {};

// 2.   剩余元素的灵活性
// 剩余元素（...）现在可以出现在元组的任意位置，而不仅仅是末尾

type Strings = [string, string];
type Numbers = [number, number];

type StrStrNumNumBool = [...Strings, ...Numbers, boolean];

const strStrNumNumBool: StrStrNumNumBool = ["a", "b", 1, 2, true];
console.log(strStrNumNumBool);

// 3. 更好的类型推断
type Arr = readonly any[];

function concat<T extends Arr, U extends Arr>(arr1: T, arr2: U) {
  return [...arr1, ...arr2];
}

const result = concat([1, 2, 3], ["hello", "world"]);
console.log(result);

// 4. 部分参数应用
function partialCall<T extends any[], U extends any[], R>(
  f: (...args: [...T, ...U]) => R,
  ...headArgs: T
) {
  return (...tailArgs: U) => f(...headArgs, ...tailArgs);
}

function f(a: number, b: string, c: boolean, d: string[]) {}

const f1 = partialCall(f, 1, "hello");
console.log(f1(true, ["world"]));
const f2 = partialCall(f, 1, "hello", true);
const f3 = partialCall(f, 1);
