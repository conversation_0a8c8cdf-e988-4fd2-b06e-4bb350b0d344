// 可变元组
// 不能将类型“[string, number, string]”分配给类型“[string, number]”。
// 源具有 3 个元素，但目标仅允许 2 个
// let [username, no]: [string, number] = ["zhang<PERSON>", 18, "海淀区上地"];

// 可变元组
// let [username, no]: [string, number, ...string[]] = [
//   "zhangsan",
//   18,
//   "海淀区上地",
//   "12345678901",
//   "12345678902",
//   "12345678903",
// ];

// 元组标签
let [username, age, ...rest]: [
  name_: string,
  age_: number,
  ...rest: any[],
  descri: string
] = [
  "zhang<PERSON>",
  18,
  "海淀区上地",
  "12345678901",
  "12345678902",
  "12345678903",
  "描述",
];
console.log(username);
console.log(age);
console.log(rest);

// const arr2: (string | number)[] = [10, 30, 50, 90, "100"] as const;
// arr2[0] = 100;
// console.log(arr2);

let [x1, ...y1] = ["a", "b", "c"];
console.log(x1);
console.log(y1);

let constnum1 = [10, 20, "30"];
let [x2, ...y2] = constnum1;
console.log(x2);
console.log(y2);

let constnum2 = [10, 20, "30"] as const;
// 类型 "readonly [10, 20, "30"]" 为 "readonly"，不能分配给可变类型 "(string | number)[]"。ts(4104)
// let [x3, ...y3]: (string | number)[] = constnum2; // 错误
// console.log(x3);
// console.log(y3);

let [x4, ...y4]: readonly [any, ...any[]] = constnum2; // 正确
console.log(x4);
console.log(y4);
// x4[0] = 100; // 错误: 无法分配到 "0" ，因为它是只读属性。ts(2540)

// readonly等效于 as const
// 返回元组（排除第一个元素）
function tail(constnum5: readonly [any, ...any[]]) {
  let [first, ...rest] = constnum5;
  console.log(first);
  console.log(rest);
  return rest;
}
const rest2 = tail(constnum2);
console.log(rest2);
