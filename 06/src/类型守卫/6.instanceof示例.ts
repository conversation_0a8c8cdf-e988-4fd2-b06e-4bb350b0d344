/**
 * 
 * 
 * 2-29-5 TS类型守卫结合应用深层掌握【真实应用场景】
顾客租赁多辆不同类型的车后如何计算租赁价格。
具体需求1:汽车租赁功能实现: 有小轿车,大巴,卡车三种类型的车,顾客可以租任意一种或多种不同类型的车,按照租用的天计算租金，
同时为了响应国家对各类车安全的管理,对在租赁期内有过各种超载，超乘客数，酒后驾车等违规的车需额外支付一定的费用。

 */

// 定义车辆类型和违规类型
type VehicleType = "car" | "bus" | "truck";
type ViolationType = "overload" | "overpassenger" | "drunkDriving";

// 基础车辆类
abstract class Vehicle {
  constructor(
    public id: string,
    public type: VehicleType,
    public dailyRate: number,
    protected baseFineRate: number = 0.1 // 基础罚金比例
  ) {}

  // 计算基础租金
  calculateBasePrice(days: number): number {
    return this.dailyRate * days;
  }

  // 抽象方法：计算违规罚金
  abstract calculateViolationFees(violations: ViolationType[]): number;

  // 获取车辆信息
  getInfo(): string {
    return `车辆ID: ${this.id}, 类型: ${this.type}, 日租金: ${this.dailyRate}元`;
  }
}

// 小轿车类
class Car extends Vehicle {
  constructor(id: string, dailyRate: number = 300) {
    super(id, "car", dailyRate, 0.15); // 小轿车罚金比例稍高
  }

  calculateViolationFees(violations: ViolationType[]): number {
    let totalFine = 0;
    for (const violation of violations) {
      switch (violation) {
        case "overpassenger":
          totalFine += this.dailyRate * 0.3; // 超员罚金
          break;
        case "drunkDriving":
          totalFine += this.dailyRate * 1.0; // 酒驾罚金
          break;
        default:
          totalFine += this.dailyRate * this.baseFineRate;
      }
    }
    return totalFine;
  }
}

// 大巴类
class Bus extends Vehicle {
  constructor(id: string, dailyRate: number = 800) {
    super(id, "bus", dailyRate, 0.2); // 大巴罚金比例更高
  }

  calculateViolationFees(violations: ViolationType[]): number {
    let totalFine = 0;
    for (const violation of violations) {
      switch (violation) {
        case "overpassenger":
          totalFine += this.dailyRate * 0.5; // 超员罚金更高
          break;
        case "drunkDriving":
          totalFine += this.dailyRate * 1.5; // 酒驾罚金
          break;
        default:
          totalFine += this.dailyRate * this.baseFineRate;
      }
    }
    return totalFine;
  }
}

// 卡车辆
class Truck extends Vehicle {
  constructor(id: string, dailyRate: number = 600) {
    super(id, "truck", dailyRate, 0.25); // 卡车罚金比例最高
  }

  calculateViolationFees(violations: ViolationType[]): number {
    let totalFine = 0;
    for (const violation of violations) {
      switch (violation) {
        case "overload":
          totalFine += this.dailyRate * 0.7; // 超载罚金
          break;
        case "drunkDriving":
          totalFine += this.dailyRate * 2.0; // 酒驾罚金最高
          break;
        default:
          totalFine += this.dailyRate * this.baseFineRate;
      }
    }
    return totalFine;
  }
}

// 租赁记录类
class RentalRecord {
  constructor(
    public vehicle: Vehicle,
    public days: number,
    public violations: ViolationType[] = []
  ) {}

  // 计算总费用
  calculateTotalPrice(): number {
    const basePrice = this.vehicle.calculateBasePrice(this.days);
    const violationFees = this.vehicle.calculateViolationFees(this.violations);
    return basePrice + violationFees;
  }

  getRecordInfo(): string {
    return `${this.vehicle.getInfo()}, 租期: ${this.days}天, 违规: ${
      this.violations.length > 0 ? this.violations.join(",") : "无"
    }, 总价: ${this.calculateTotalPrice()}元`;
  }
}

// 顾客租赁管理类
class CustomerRentalManager {
  private rentalRecords: RentalRecord[] = [];

  // 添加租赁记录
  addRentalRecord(record: RentalRecord): void {
    this.rentalRecords.push(record);
  }

  // 计算顾客总费用
  calculateTotalCost(): number {
    return this.rentalRecords.reduce((total, record) => {
      return total + record.calculateTotalPrice();
    }, 0);
  }

  // 获取租赁详情
  getRentalDetails(): string {
    return this.rentalRecords
      .map((record) => record.getRecordInfo())
      .join("\n");
  }
}

// 类型守卫：判断是否为Car类型
function isCar(vehicle: Vehicle): vehicle is Car {
  return vehicle.type === "car";
}

// 类型守卫：判断是否为Bus类型
function isBus(vehicle: Vehicle): vehicle is Bus {
  return vehicle.type === "bus";
}

// 类型守卫：判断是否为Truck类型
function isTruck(vehicle: Vehicle): vehicle is Truck {
  return vehicle.type === "truck";
}

// 示例使用
function main() {
  // 创建车辆
  const car1 = new Car("C001", 300);
  const bus1 = new Bus("B001", 500);
  const truck1 = new Truck("T001", 1000);

  // 创建顾客租赁管理器
  const customerManager = new CustomerRentalManager();

  // 添加租赁记录
  customerManager.addRentalRecord(new RentalRecord(car1, 5, ["drunkDriving"]));
  customerManager.addRentalRecord(new RentalRecord(bus1, 3, ["overpassenger"]));
  customerManager.addRentalRecord(
    new RentalRecord(truck1, 2, ["overload", "drunkDriving"])
  );

  // 输出租赁详情和总费用
  console.log("租赁详情:");
  console.log(customerManager.getRentalDetails());
  console.log(`\n顾客总费用: ${customerManager.calculateTotalCost()}元`);

  // 演示类型守卫的使用
  const vehicleList: Vehicle[] = [car1, bus1, truck1];
  vehicleList.forEach((vehicle: Vehicle) => {
    const id = vehicle.id;
    if (isCar(vehicle)) {
      console.log(`${id}是小轿车，适合家庭出行`);
    } else if (isBus(vehicle)) {
      console.log(`${id}是大巴，适合团体旅游`);
    } else if (isTruck(vehicle)) {
      console.log(`${id}是卡车，适合货物运输`);
    }
  });
}

function mainInstanceof() {
  // 创建车辆实例
  const car1 = new Car("C001");
  const bus1 = new Bus("B001");
  const truck1 = new Truck("T001");

  // 用 instanceof 判断车辆类型
  console.log(car1 instanceof Car); // true
  console.log(bus1 instanceof Bus); // true
  console.log(truck1 instanceof Truck); // true
  console.log(car1 instanceof Vehicle); // true（因为 Car 继承自 Vehicle）

  // 遍历车辆列表，用 instanceof 做类型区分
  const vehicles: Vehicle[] = [car1, bus1, truck1];
  vehicles.forEach((vehicle) => {
    if (vehicle instanceof Car) {
      // 这里 TypeScript 会自动推断 vehicle 是 Car 类型
      console.log(
        `车辆 ${vehicle.id} 是小轿车，日租金 ${vehicle.dailyRate} 元`
      );
      // 可以安全调用 Car 类特有的方法（如果有的话）
    } else if (vehicle instanceof Bus) {
      // 推断为 Bus 类型
      console.log(`车辆 ${vehicle.id} 是大巴，日租金 ${vehicle.dailyRate} 元`);
    } else if (vehicle instanceof Truck) {
      // 推断为 Truck 类型
      console.log(`车辆 ${vehicle.id} 是卡车，日租金 ${vehicle.dailyRate} 元`);
    }
  });

  // 计算费用时也可以用 instanceof 做特殊处理
  const calculateSpecialFee = (vehicle: Vehicle) => {
    if (vehicle instanceof Truck) {
      // 卡车可能有额外的保险费
      return vehicle.dailyRate * 0.1;
    } else if (vehicle instanceof Bus) {
      // 大巴可能有额外的路桥费
      return vehicle.dailyRate * 0.05;
    }
    return 0;
  };

  vehicles.forEach((vehicle) => {
    const specialFee = calculateSpecialFee(vehicle);
    console.log(`车辆 ${vehicle.id} 的额外费用：${specialFee} 元`);
  });
}

// 执行示例
mainInstanceof();
