function Person(phone, age) {
  this.age = age; // age实例属性
  this.phone = phone; // phone实例属性
  this.showone = function () {}; // showone实例方法
}

Person.prototype.doEat = function () {
  console.log("电话", this.phone);
};

let person = new Person("123", 18);
console.log(person, typeof person);

// new 一个实例对象的底层3步
//1 创建一个Object对象
// var obj = new Object()

var obj = {};

// 2. 让新创建的对象的 __proto__变量指向Person原型对象空间
obj.__proto__ = Person.prototype;

// 3. 借用Person构造函数 为 obj对象变量增加age和phone属性
Person.call(obj, "123", 18);

console.log("obj: ", obj);
