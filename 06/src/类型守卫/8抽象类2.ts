// 通过抽象类实现接口适配器，让子类可以只实现自己需要的方法

interface MouseListenerProcess {
  mouseRelease(e: Event): void; // 鼠标释放事件
  mouseClick(e: Event): void; // 鼠标点击事件
  mouseMove(e: Event): void; // 鼠标移动事件
  mouseWheel(e: Event): void; // 鼠标滚动事件
  mouseDrag(e: Event): void; // 鼠标拖拽事件

  mouseEnter(e: Event): void; // 鼠标进入事件
  mouseLeave(e: Event): void; // 鼠标离开事件
}

abstract class MouseAdapter implements MouseListenerProcess {
  mouseRelease(e: Event): void {}
  mouseMove(e: Event): void {}
  mouseWheel(e: Event): void {}
  mouseDrag(e: Event): void {}
  mouseEnter(e: Event): void {}

  abstract mouseClick(e: Event): void;
  abstract mouseLeave(e: Event): void;
}

class myMouseListenerProcess extends MouseAdapter {
  mouseClick(e: Event): void {
    throw new Error("Method not implemented.");
  }
  mouseLeave(e: Event): void {
    throw new Error("Method not implemented.");
  }
}

export {};
