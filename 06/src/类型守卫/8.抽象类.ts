// 抽象类是专门用来被继承的类，不能被实例化
abstract class People {
  // ! 是非空断言操作符，表示该属性一定会被赋值，告诉 TypeScript 编译器不要报错
  // 等同于下面这种写法:
  // public name: string = '';
  // 或者在构造函数中初始化:
  // constructor() { this.name = ''; }
  public name!: string;

  constructor(name: string) {
    this.name = name;
  }

  // 抽象类中可以有抽象方法
  public abstract eat(): void;

  // 抽象类中可以有非抽象的方法
  public step() {
    console.log("走路");
  }
}
// 抽象类不能被实例化
// let people = new People();

class ChinesePeople extends People {
  constructor(name: string) {
    super(name);
  }

  public eat() {
    console.log("用筷子吃饭");
  }
}

let chinesePeople = new ChinesePeople("张三");
chinesePeople.eat();
const name = chinesePeople.name;
console.log(name);

export {};
