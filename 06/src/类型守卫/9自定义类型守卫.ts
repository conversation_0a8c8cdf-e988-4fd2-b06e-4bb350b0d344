/**
 * 
 *  自定义守卫语法格式：

 function 函数名（形参： 参数类型）：形参 is 类型 {
    return  true or false;
 }

 当返回值为true时，表示形参的类型就是is后面的类型 ***

 */

interface Circle {
  kind: "circle";
  radius: number;
}

interface Square {
  kind: "square";
  sideLength: number;
}

type Shape = Circle | Square; // 联合类型

// 非自定义类型守卫
function getArea(shape: Shape) {
  // 每次调用该函数，都需要进行类型判断
  if (shape.kind === "circle") {
    return Math.PI * shape.radius * shape.radius;
  } else {
    return shape.sideLength * shape.sideLength;
  }
}

// 自定义类型守卫（ shape is Circle 类型谓词）
function isCircle(shape: Shape): shape is Circle {
  return shape.kind === "circle";
}

function calculateArea(shape: Shape) {
  // 自定义类型守卫,封装
  if (isCircle(shape)) {
    return Math.PI * shape.radius ** 2;
  } else {
    return shape.sideLength ** 2;
  }
}

const myCircle: Circle = { kind: "circle", radius: 5 };
const mySquare: Square = { kind: "square", sideLength: 10 };

console.log(getArea(myCircle));
console.log(getArea(mySquare));

calculateArea(myCircle);
calculateArea(mySquare);

export {};
