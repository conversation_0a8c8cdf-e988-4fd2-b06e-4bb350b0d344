// 大厂TS类型守卫晋级考核题【综合题】:
// 请编写一个操作对象方法和属性的函数实现以下功能
// 1.当对象字符串属性有空格时就去掉空格后输出，
// 2.当遇到对象函数（方法）时就执行,其他数据类型的属性一律直接输出1
// 3.只有对象中包含allowoutput属性时,才允许输出。
// 4、函数接收到外部传入的null,undefined,{}时，直接输出不是一个合法的对象。
// // 考核点:1.考核对类型守卫的熟练运用程度 2.静态方法

/**
 * 断言
 * 类型判断： typeof
 * 属性或者方法或判断： in
 * 实例判断 instanceof
 * 字面量相等判断 == === != !==
 */

interface TestInter {
  username: string;
  age: number;
  eat(): void;
  allowoutput?: 1;
}

let testObj: TestInter = {
  username: "  123  ",
  age: 18,
  eat() {
    console.log("eat");
  },
  allowoutput: 1,
};

class StringUtils {
  static trim(str: string) {
    return str.replace(/\s+/g, "");
  }
}

function ProcessObjectOutput(obj: any) {
  if (obj === null || obj === undefined || Object.keys(obj).length === 0) {
    console.log("不是一个合法的对象");
    return;
  }

  if ("allowoutput" in obj) {
    console.log(" allowoutput in obj");

    Object.keys(obj).forEach((key) => {
      if (typeof obj[key] === "string") {
        console.log(StringUtils.trim(obj[key]));
      } else if (typeof obj[key] === "function") {
        obj[key]();
      } else {
        console.log(key, obj[key]);
      }
    });
    return obj;
  }
}

ProcessObjectOutput(testObj);

export {};
