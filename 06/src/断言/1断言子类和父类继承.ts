class People {
  public myusername!: string;
  public myage!: number;
  public address!: string;

  constructor() {}
}

class Stu extends People {
  public username!: string;
  public age!: number;
  public address!: string;

  constructor(username: string, age: number, address: string) {
    super();
  }

  study() {}
}

let people = new People();
// let result = people as Stu; // 断言
let result = <Stu>people; // 类型转换
result.study();

let stu = new Stu("张三", 18, "北京");
stu as People;
