/**
 *
 * any 可以充当所有类的父类，也可以充当所有类的子类
 * unkonw 可以充当所有类的父类，但不能充当所有类的子类
 *
 *  */

let symid = Symbol("objid");
console.log(symid);

let obj = {
  [symid]: "123456",
  name: "张三",
  age: 18,
  address: "北京",
  phone: "12345678901",
  kk() {
    console.log("kk");
  },
};

let username = obj["name"];
// let objid = obj[symid as unknown];
let objid = obj[symid as any];

console.log(objid);

function add(one: string | number, two: string | number) {
  //   return one + two; // 运算符“+”不能应用于类型“string | number”和“string | number”。ts(2365)
  return (one as any) + (two as any);
}

console.log(add(1, "2"));
console.log(add(1, 2));

export {};
