/**
 * 4.如果A是类，B 是接口，并且A类没有实现了B接口，则断言关系和第2项的规则完全相同
 */

interface People {
  username: string;
  age: number;
  address: string;
  phone: string;
}

class Student {
  public username!: string;
  public age!: number;
  public address!: string;
  public phone!: string;

  constructor(username: string, age: number, address: string) {
    this.address = address;
    this.age = age;
    this.address = address;
  }

  public kk() {
    console.log("kk");
  }
}

let people: People = {
  username: "张三",
  age: 18,
  address: "北京",
  phone: "12345678901",
};

const studentConvert = <Student>people; // 类型转换OK，
studentConvert.kk(); // 但是调用kk方法会报错，因为kk是Students类的方法，不是People类的方法

const studentConvert2 = people as Student; // 类型转换OK，

let student = new Student("张三", 18, "北京");
student as People;
export {};
