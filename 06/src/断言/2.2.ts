class UserA {
  public name: string;
  private id: number; // 私有成员

  constructor(name: string, id: number) {
    this.name = name;
    this.id = id;
  }
}

class UserB {
  public name: string;
  private id: string; // 私有成员（类型不同）

  constructor(name: string, id: string) {
    this.name = name;
    this.id = id;
  }
}

// ❌ 私有成员不兼容，即使 public 成员相同
// const userA: UserA = new UserB("张三", "123"); // 类型错误
const uesrA = new UserA("张三", 123) as UserB; // 类型断言
