/**
 * 3.*如果A是类，B是接口，并且A类实现了B接口【implements】**，
 * 则A的对象变量可以断言成B接口类型，
 * 同样B接口类型的对象变量也可以断言成A类型。
 */
interface People {
  username: string;
  age: number;
  address: string;
  phone: string;
}

class Students implements People {
  public username!: string;

  public age!: number;

  public address!: string;

  public phone!: string;

  constructor(username: string, age: number, address: string) {
    this.address = address;
  }

  public kk() {
    console.log("kk");
  }
}

const student = new Students("张三", 18, "北京");
const peopleConvert = student as People;
console.log(peopleConvert.address);

let people: People = {
  username: "张三",
  age: 18,
  address: "北京",
  phone: "12345678901",
};
const studentConvert = <Students>people; // 类型转换OK，
studentConvert.kk(); // 但是调用kk方法会报错，因为kk是Students类的方法，不是People类的方法

export {};
