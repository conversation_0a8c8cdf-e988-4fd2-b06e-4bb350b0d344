/**
 * 重点**********
 * 2.如果 A，B 如果是类，但没有继承关系
两个类中的任意一个类的所有的 public 实例属性【不包括静态属性】加上
所有的 public 实例方法和另一个类的所有 public 实例属性加上
所有的 public 实例方法完全相同或是另外一个类的子集，
则这两个类可以相互断言，否则这两个类就不能相互断言。

 */

class Peoples {
  constructor(
    public username: string,
    public age: number,
    public address: string
  ) {}
}

class Students {
  //   public username!: string;

  public age!: number;

  public address!: string;

  public phone!: string;

  constructor(username: string, age: number, address: string) {
    // super(username, age, address)
    this.address = address;
  }

  public study() {}
}

let p = new Peoples("张三", 18, "北京");
let stuConvert = p as Students;
stuConvert.study();

let s = new Students("张三", 18, "北京");
let peopleConvert = s as Peoples;
console.log(peopleConvert.address);
