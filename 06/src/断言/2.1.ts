/**
 * B 是 A 的子集 → 则可以相互断言(仅包含公共的属性和方法)
 */

class Animal {
  public name: string;

  constructor(name: string) {
    this.name = name;
  }
  public eat() {}
}

class Dog {
  public name: string;

  constructor(name: string) {
    this.name = name;
  }
  public eat() {}
  public bark() {} // Dog 有额外方法
}

// ✅ Animal 是 Dog 的子集（Animal 的成员都在 Dog 中）
const animal: Animal = new Dog("旺财");

// ❌ Dog 不是 Animal 的子集（bark 方法不在 Animal 中）
// 类型声明不支持，但可以通过断言
const dog: Dog = new Animal("旺财"); // 类型错误
const dog2 = new Animal("旺财") as Dog; // 类型断言
