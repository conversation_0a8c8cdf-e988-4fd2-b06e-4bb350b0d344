/**
 * 8.多个类组成的联合类型如何断言?例如:let vechile:Car |Bus|Trunck。，vechile 可以断言成其中任意-种数据类型。 例如 vechile as Car, vechile as Bus , vechile as Trunck 。

 */

class Vechile {
  name: string;
  constructor(name: string) {
    this.name = name;
  }
  run() {
    console.log(`${this.name} is running`);
  }

  calculateRent() {}
}

class Car extends Vechile {
  constructor(name: string) {
    super(name);
  }
  run() {
    console.log(`${this.name} is running fast`);
  }

  stop() {
    console.log(`${this.name} has stopped`);
  }
}

class Bus extends Vechile {
  constructor(name: string) {
    super(name);
  }
  run() {
    console.log(`${this.name} is running slowly`);
  }

  stop() {
    console.log(`${this.name} has stopped`);
  }

  checkIsOverNum(isOverNum: boolean) {
    console.log(`isOverNum: ${isOverNum}`);
  }

  calculateRent() {
    console.log(`calculateRent`);
  }
}

class Trunck extends Vechile {
  constructor(name: string) {
    super(name);
  }
  run() {
    console.log(`${this.name} is running slowly`);
  }

  stop() {
    console.log(`${this.name} has stopped`);
  }

  calculateRent() {
    console.log(`calculateRent`);
  }
}

class Customer {
  rentVechile(vechile: Bus | Car | Trunck) {
    const vechileConvert = vechile as Bus; // 断言成 Bus 类型
    vechileConvert.checkIsOverNum(true); // 调用Bus独有的方法

    (vechile as Vechile).calculateRent(); // 断言成 Vechile 类型
  }
}

export {};
