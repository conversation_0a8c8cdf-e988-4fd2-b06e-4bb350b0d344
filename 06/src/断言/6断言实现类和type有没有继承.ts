/**
 * 6.如果 A 是类，B是 type 定义的对象数据类型，并且A类没有实现 Btype定义的数据类型，则断言关系和第2项的规则完全相同。
 */
type People = {
  username: string;
  age: number;
  address: string;
  phone: string;
};

class Stu {
  public username!: string;
  public age!: number;
  public address!: string;
  public phone!: string;

  constructor(username: string, age: number, address: string) {
    this.username = username;
    this.age = age;
    this.address = address;
  }

  public kk() {
    console.log("kk");
  }
}

const student = new Stu("张三", 18, "北京");
const studentConvert = <Stu>student; // 类型转换OK，
studentConvert.kk();

const studentConvert2 = student as People; // 断言OK，但是调用kk方法会报错，因为kk是Students类的方法，不是People类的方法

export {};
