/**
 * 如果 A 是类，B 是type 定义的对象数据类型
 * 【就是引用数据类型，例如 Array,对象，不能是基本数据类型，
 * 例如 string，number,boolean】，并且有A类实现了 Btype 定义的数据类型【implements】，
 * 则A的对象变量可以断言成Btype 定义的对象数据类型，
 * 同样 Btype 定义的对象数据类型的对象变量也可以断言成 A 类型。
 */

/**
 * 类型包含类的所有属性（可断言为类）
 */
type PersonType = {
  name: string;
  sayHello(): void;
};

class PersonClass implements PersonType {
  constructor(public name: string, public age: number) {}
  sayHello() {
    console.log(`Hello, ${this.name}`);
  }
}

// ✅ 类型 → 类实例（结构兼容）
const personType: PersonType = {
  name: "Alice",
  sayHello: () => {
    console.log("Hello, Alice");
  },
};
const personClass: PersonClass = personType as PersonClass;
personClass.sayHello(); // 正常运行
