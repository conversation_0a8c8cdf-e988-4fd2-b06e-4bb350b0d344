/**
 * 10.对于第9项思考题中的关于使用静态属性或静态方法的解决方案绝对不能用在学生，顾客其他应用场景那样会导致三个比较严重的问题，以学生对象为例:
1.浪费了很多不必要的内存空间
运行一开始就为大量的静态属性和大量的静态方法分配内存空间【但很可能某个静态方法一直没有使用，白白的一直占用着内存空间】
2.无法展示一个学生一个对象的直观效果，完全失去了对象来描述实体的优势!
3.最严重的问题是:属性值一变则都变
所有操作都在用一个静态方法空间来完成某种功能，一旦某个操作改变了静态方法中的某个值，比如改变了学生姓名，则其他操作访问到这个静态变量看到的结果全变了。 
 */


/**
 * 下面将结合学生对象的场景，详细解释使用静态属性或静态方法可能导致的三个严重问题，并给出相应示例。

1. 浪费不必要的内存空间
静态属性和静态方法属于类本身，在程序启动、类加载时就会被分配内存空间，且在整个程序运行期间一直存在。若定义了大量静态属性和静态方法，然而部分静态方法可能从未被调用，这就会造成内存空间的浪费。
 
在代码中，someUnusedMethod 静态方法在整个程序运行过程中可能都不会被调用
，但在类加载时，它和其他静态属性、静态方法一样会被分配内存空间，从而造成内存浪费。

*/


class Student {
  // 静态属性，模拟学生信息
  static student_name: string = '';
  static age: number = 0;

  // 静态方法，设置学生姓名
  static setName(newName: string) {
    Student.student_name = newName;
  }

  // 静态方法，设置学生年龄
  static setAge(newAge: number) {
    Student.age = newAge;
  }

  // 静态方法，可能永远不会被调用
  static someUnusedMethod() {
    console.log('这是一个可能永远不会被调用的静态方法');
  }
}

// 仅使用部分静态方法
Student.setName('张三');
Student.setAge(18);




/**
 * 2. 无法展示一个学生一个对象的直观效果
面向对象编程的优势之一是可以用对象来描述实体，每个对象都有自己独立的状态和行为。
但如果使用静态属性和静态方法，所有学生共享相同的属性和方法，
无法体现每个学生的独特性，失去了对象描述实体的优势。


解释
按照面向对象的思想，每个学生应该是独立的对象，有自己的姓名和年龄。但在上述代码中，使用静态属性来存储学生信息，
当修改学生信息时，所有学生共享的信息都会被改变，无法区分不同的学生对象，失去了对象描述实体的直观效果。
 */
class Student2 {
  static student_name: string = '';
  static age: number = 0;

  static setInfo(name: string, age: number) {
    Student2.student_name = name;
    Student2.age = age;
  }

  static showInfo() {
    console.log(`姓名: ${Student2.student_name}, 年龄: ${Student2.age}`);
  }
}

// 创建两个学生信息
Student2.setInfo('张三', 18);
Student2.showInfo(); // 输出: 姓名: 张三, 年龄: 18

Student2.setInfo('李四', 20);
Student2.showInfo(); // 输出: 姓名: 李四, 年龄: 20


/**
 * 3. 属性值一变则都变
由于静态属性被类的所有实例共享，一旦某个操作修改了静态属性的值，其他操作访问该静态属性时，
获取到的都是修改后的值。

解释
在上述代码中，name 是静态属性，当调用 changeName 方法修改学生姓名时，
所有对 name 属性的访问都会得到修改后的值。这意味着无法为不同的学生对象维护独立的姓名信息，不符合实际需求。

综上所述，在学生、顾客等应用场景中，滥用静态属性和静态方法会带来诸多问题，
应优先使用实例属性和实例方法来描述对象的状态和行为
 */
class Student3 {
  static stuent_name: string = '';

  static changeName(newName: string) {
    Student3.stuent_name = newName;
  }

  static showName() {
    console.log(`学生姓名: ${Student3.stuent_name}`);
  }
}

// 第一次设置学生姓名
Student3.changeName('张三');
Student3.showName(); // 输出: 学生姓名: 张三

// 第二次修改学生姓名
Student3.changeName('李四');
Student3.showName(); // 输出: 学生姓名: 李四




