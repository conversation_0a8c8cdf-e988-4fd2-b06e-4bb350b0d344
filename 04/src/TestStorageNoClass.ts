// 构建单件localStorage类（真实应用场景）

localStorage.setItem('name', '张三');

let loginInfoObj = {username: "<PERSON><PERSON><PERSON>", password: "123456"};
localStorage.setItem("loginInfo", JSON.stringify(loginInfoObj));

 // 问题1： 代码零散
 // 问题2： 代码可读性差
 // 问题3： 代码维护性差
 // 问题4： 代码复用性差： JSON.parse 和 JSON.stringify 是重复的代码 可以直接放到类中
let value = localStorage.getItem("loginInfo");
value!=null ? JSON.parse(value):null;
