// import MylocalStorage from "./MylocalStorage";


// let mylocalStrage = new MylocalStorage()
// mylocalStrage.setItem("name","zhangsan")
// mylocalStrage.setItem("age",18)
// mylocalStrage.setItem("sex","男")

// mylocalStrage.setItem("loginUSerInfo", {username:"zhang<PERSON>", password:"123456"})

// mylocalStrage.getItem("name")

// export default mylocalStrage



import MylocalStorageSingle from "./MylocalStorageSingle";

// 单例模式
// const mylocalStrage = MylocalStorageSingle.getInstance()
// mylocalStrage.setItem("name","zhangsan")
// mylocalStrage.setItem("age",18)
// mylocalStrage.setItem("sex","男")

// 静态方法改变静态属性的值，这个改变是全局的，所有的静态方法都可以访问到这个值
MylocalStorageSingle.setCount()
console.log(MylocalStorageSingle.getCount())

MylocalStorageSingle.setCount()
console.log(MylocalStorageSingle.getCount())

import {count} from './Test2'
console.log(count)


