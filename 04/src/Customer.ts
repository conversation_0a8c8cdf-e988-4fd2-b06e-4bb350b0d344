/**
 * 应用2:当类中某个方法没有任何必要使用任何对象属性时，而且使用了对象属性反而让这个方法的逻辑不正确，那既如此，
 * 就应该禁止这个方法访问任何对象属性和其他的对象方法，这时就应该把这个方法定义为静态方法。
 * 例如:一个顾客类的购买方法【buy方法】中肯定要允许访问顾客姓名或其他顾客微信这些对象属性，
 * 这样的方法我们就需要定义在原型对象属性上，
 * 但如果顾客类中的 阅读顾客积分公告方法【readNotice方法] 是针对全体顾客的公告方法，
 * 就应该定义为静态方法，方法内部就应该禁止出现任何具体的对象属性。如果在这样的方法中使用了顾客的某个属性，
 * 比如用了顾客姓名，那么这个方法逻辑就不正确【这个方法就会说:你让我向全体顾客展示公告，
 * 你我要知道每个顾客姓名做什么?】。
 * 所以我们应该让这样的方法禁止访问对象属性和其他的对象方法，那就应该设置为静态方法。
 */

/**
 * 代码解释
1. 原型方法 buy
buy 方法定义在原型对象上，因为它需要访问 Customer 类实例的对象属性 name 和 wechat。每个顾客实例调用 buy 方法时，会输出该顾客的姓名和微信信息，不同的实例调用该方法会有不同的输出结果，所以这个方法依赖于对象属性，适合作为原型方法。

2. 静态方法 readNotice
readNotice 方法定义为静态方法，它是针对全体顾客的公告方法，不需要访问任何具体顾客的对象属性。该方法内部使用了静态属性 notice 来存储公告内容，通过类名 Customer 直接访问。如果在 readNotice 方法中尝试访问对象属性，如 this.name，会导致逻辑错误，因为公告是面向全体顾客的，不需要知道每个顾客的具体信息。所以，将 readNotice 方法定义为静态方法，禁止它访问对象属性和其他对象方法。

总结
原型方法：当方法需要访问对象属性或对象方法，并且不同实例调用该方法可能有不同行为时，应将方法定义在原型对象上。
静态方法：当方法不需要访问对象属性和对象方法，且其逻辑与具体实例无关，是针对整个类的操作时，应将方法定义为静态方法。
 * 
 */

class Customer {
  // 对象属性：顾客姓名和微信
  name: string;
  wechat: string;

  // 构造函数，初始化对象属性
  constructor(name: string, wechat: string) {
    this.name = name;
    this.wechat = wechat;
  }

  // 原型方法：购买方法，需要访问对象属性
  buy() {
    console.log(`${this.name}（微信：${this.wechat}）正在购买商品。`);
  }

  // 静态属性：顾客积分公告内容
  private static notice: string = '全体顾客请注意，本月积分双倍活动开启！';

  // 静态方法：阅读顾客积分公告方法，针对全体顾客，不访问对象属性
  static readNotice() {
    console.log(Customer.notice);
  }
}

// 创建顾客实例
const customer1 = new Customer('张三', 'zhangsan_wechat');
const customer2 = new Customer('李四', 'lisi_wechat');

// 调用原型方法
customer1.buy(); // 输出: 张三（微信：zhangsan_wechat）正在购买商品。
customer2.buy(); // 输出: 李四（微信：lisi_wechat）正在购买商品。

// 调用静态方法
Customer.readNotice(); // 输出: 全体顾客请注意，本月积分双倍活动开启！