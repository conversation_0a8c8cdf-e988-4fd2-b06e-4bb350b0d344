/**
 * 构建单件localStorage类（真实应用场景）-懒汉式
 * 第一步： 把构造器设置为私有的，不允许外部来创建类的实例（对象）
 * 第二步： 至少应该提供一个外部访问的方法或属性，外部可以通过这个方法或属性来访问类的实例（对象），
 *  所以应该把这个方法设置为静态方法
 * 第三步：外部调用第二步提供的静态方法来获取一个对象
 */
export default class MylocalStorageSingle {

    private static count: number = 0;

    public static getCount(): number {
        return MylocalStorageSingle.count;
    }

    public static setCount(): void {
        MylocalStorageSingle.count += 3;
    }

    private static instance: MylocalStorageSingle;
    private constructor() {
        // 防止外部通过new来创建类的实例（对象）
        console.log("这是一个单件localStorage类 实例化了一次")
     }

    public static getInstance(): MylocalStorageSingle {
        if (!MylocalStorageSingle.instance) {
            MylocalStorageSingle.instance = new MylocalStorageSingle();
        }

        return MylocalStorageSingle.instance;

    }

    public setItem(key: string, value: any): void {
        localStorage.setItem(key, JSON.stringify(value));
    }

    public getItem(key: string): any {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    }
}