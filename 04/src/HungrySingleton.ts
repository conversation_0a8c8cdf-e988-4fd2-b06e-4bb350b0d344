/**
 * 饿汉式单例类
 * 类加载时就创建单例实例
 */

/**
 * 
 * 代码解释
静态属性 instance：private static instance: HungrySingleton = new HungrySingleton(); 这行代码在类加载时就创建了 HungrySingleton 类的实例，并将其赋值给静态属性 instance。private 修饰符确保该属性只能在类内部访问。
私有构造函数：private constructor() {} 防止外部代码直接使用 new 关键字创建 HungrySingleton 类的实例，保证单例的唯一性。
静态方法 getInstance：该方法用于获取单例实例，直接返回静态属性 instance 存储的实例。
示例方法 showMessage：这是一个普通的实例方法，用于演示单例实例的使用。
 */
class HungrySingleton {
  // 静态属性，在类加载时就创建单例实例
  private static instance: HungrySingleton = new HungrySingleton();

  // 私有构造函数，防止外部通过 new 关键字创建实例
  private constructor() {}

  // 静态方法，用于获取单例实例
  public static getInstance(): HungrySingleton {
    return HungrySingleton.instance;
  }

  // 示例方法
  public showMessage() {
    console.log('这是饿汉式单例实例的方法');
  }
}

export default HungrySingleton;
