// 1. 对现有的数组进行封装，让数组增删改变得更好好用
// 2. 提供get方法 remove方法 显示方法 add方法
// 其中需求中的remove方法有两个，我们用方法重载来实现


export default class ArrayList {
    
    // 第一步：定义一个数组，用来存储数据

    constructor(public element: Array<Object>) {
       
    }

    // 第二步：定义一个方法，用来获取数组中的元素
    get(index: number) {
        return this.element[index];
    }

   
    remove(index: number): number;
    remove(element: object): object;

    // 第三步：定义一个方法，用来删除数组中的元素
    remove(value: number | object ): number | object {
       this.element = this.element.filter((element, index) => {
        if (typeof value === 'number') {
            return index !== value;
        }
        else {
            return element !== value;
        }
       })
       return value; 
    }

    // 第四步：定义一个方法，用来添加元素到数组中
    add(element: object): void {
        this.element.push(element);
    }

    // 第五步：定义一个方法，用来显示数组中的元素
    display(): void {
       this.element.forEach((element, index) => {
        console.log(`index: ${index}, element: ${element}`);
       })
    }
}

let stuOne = { name: '张三', age: 18 };
let stuTwo = { name: '李四', age: 20 };
let stuThree = { name: '王五', age: 22 };

let arrayList = new ArrayList([stuOne, stuTwo, stuThree]);
arrayList.display();

// 如果是根据数字去删除元素， remove方法返回的是一个数字
// 如果是根据对象去删除元素， remove方法返回的是一个对象
arrayList.remove(1);
arrayList.display();
arrayList.remove(1);
arrayList.display();

arrayList.remove(stuOne);
arrayList.display();
