# 第3章 TS类方法代码优化的三大法宝： 函数重载、类方法重载、类构造器重载
## 2-11 函数重载 或方法重载
著名前端流行框架底层都用到函数重载，例如:Vue3 底层源码就多处使用到带泛型的函数重载【对于泛型先知晓下即可，我们会在第4章我们会融合 vue3 源码来深度讲解泛型函数重载，本章深度讲解的是非泛型的函数重载，掌握好了泛型函数重载，就具备了学习泛型函数重载的基础】，很多前端面试更是拿函数重载作为考核求职者 TS 技能是否扎实的标准之一，如果你不掌握函数重载，等于你的TS 技能有缺失，技能不过关
函数重载或方法重载适用于完成项目种某种相同功能但细节又不同的应用场景【先了解即可，后面我们会结合真实应用场景讲解】我们举一个生活中的例子让同学们先有个印象，比如:吃饭是一个函数，表示一个吃饭功能，但西方人用又子，中国人用筷子，新疆人甚至有地方用手抓，这就是细节不同，那如果我们可以用函数重载来解决。
不管现阶段你公司的项目中是否用到了函数重载和方法重载【如果没有用，多半是公司不少人用的并不熟练才不用的缘故】，如果学完后，你能适时给公司提建议，建议项目中合适的场景中使用函数重载并说明原因，你的建议应该很受欢迎!

ps:面试中，函数重载和方法重载是一个很重要的考点，所以我们要重点讲解

优势：
1. 结构分明
2. 各司其职，自动提示方式和属性
3. 更利于功能扩展

## 2-12 实现微信消息发送的函数 【真实应用场景, 一站理解函数重载优势】
真实应用需求:有一个获取微信消息发送接口消息查找函数，根据传入的参数从数组中查找数据，如果入参为数字，就认为消息 id，然后从从后端数据源中找对应id 的数据并返回，否则当成类型，返回这一类型的全部消息。





## 1.TS 函数重载(function signature overload)定义
TS 的函数重载比较特殊，和很多其他后端语言的方法重载相比，多了不少规则。学习函数重载，先要了解什么是函数签名，定义如下:
*函数签名* [function signature ]:函数签名=函数名称+函数参数+函数参数类型+返回值类型四者合成。在 TS函数重载中，包含了实现签名和重载签名，实现签名是一种函数签名，重载签名也是一种函数签名。
关于函数重载的定义，我们先来看一个很多其他资料提供的不完整且模糊的TS函数重载定义:
*不完整模糊的 TS 函数重载定义* :一组具有相同名字，不同参数列表的和返回值无关的函数。
*完整的函数重载定义*:包含了以下规则的一组函数就是TS函数重载 【规则内容多，大家要多记，多实践方可

**规则1**:由一个实现签名+一个或多个重载签名合成。
**规则2**:但外部调用函数重载定义的函数时，只能调用重载签名，不能调用实现签名，这看似矛盾的规则，其实 是TS 的规定:实现签名下的函数体是给重载签名编写的，实现签名只是在定义时起到了统领所有重载签名作用，在执行调用时就看不到实现签名了。
**规则3**:调用重载函数时，会根据传递的参数来判断你调用的是哪一个函数
**规则4**:只有一个函数体，只有实现签名配备了函数体，所有的重载签名都只有签名，没有配备函数体。
和0一参数米利扣川一整的力

**规则5**:关于参数类型规则完整总结如下:
实现签名参数个数可以少于重载签名的参数个数，但实现签名如果准备包含重载签名的某个位置的参数 ，那实现签名就必须兼容所有重载签名该位置的参数类型【联合类型或 any 或 unknown 类型的一种】
**规则6**:关于重载签名和实现签名的返回值类型规则完整总结如下:
必须给重载签名提供返回值类型，TS 无法默认推导。
提供给重载签名的返回值类型不一定为其执行时的真实返回值类型，可以为重载签名提供真实返回值类型，也可以提供 void 或 unknown 或 any 类型，如果重载签名的返回值类型是 void 或 unknown 或 any 类型，那么将由实现签名来决定重载签名执行时的真实返回值类型。 当然为了调用时能有自动提示+可读性更好+避免可能出现了类型强制转换，强烈建议为重载签名提供真实返回值类型。
不管重载签名返回值类型是何种类型【包括后面讲的泛型类型】，实现签名都可以返回 any 类型 或 unknown类型，当然一般我们两者都不选择，让TS 默认为实现签名自动推导返回值类型。






## 2-14 【TS 方法重载】方法重载在 Java 简易版 ArrayList 中的经典应用实现【ArrayList 可弥补 Set 取值不方便短板，比 Set 删除功能更方便】1
### 1.方法和函数区别，理解方法签名
方法:方法是一种特定场景下的函数，由对象变量【实例变量】直接调用的函数都是方法。比如:
1.函数内部用 this 定义的函数是方法;
2.TS 类中定义的函数是方法【TS 类中定义的方法就是编译后 JS 底层 prototype 的一个函数】3.接口内部定义的函数是方法【注意:不是接口函数】;
4.type 内部定义的函数是方法【注意:不是 type 函数】方法签名:和函数签名一样，方法签名=方法名称+方法参数+方法参数类型+返回值类型四者合成。

### 2. Javā简易版 ArrayList 类 和其中的方法重载代码实现




# 2-15 深入构造器重载+真实应用场景

## 1.构造器 【构造函数】重载的意义
构造器重载和函数重载使基本相同，主要区别是:构造器没有返回值这一说，而且是在对象创建出来之后，但是还没有赋值给对象变量之前被执行，一般用来给对象属性赋值。
我们知道在TS 类中只能定义一个构造器，但实际应用时，TS 类在创建对象时经常需要用到有多个构造器的场景，比如:我们计算一个正方形面积，创建正方形对象，可以给构造器传递宽和高，也可以给构造器传递一个包含了宽和高的形状参数对象，这样需要用构造器重载来解决，而面试中也多次出现过关于TS构造器重载的考察，主要考察求职者对重载+构造器的综合运用能力。

## 2.构造器是方法吗?
我们说对象调用的才是方法，但是TS 构造器是在对象空间地址赋值给对象变量之前被调用，而不是用来被对象变量调用的，所以构造器(constructor )可以说成构造函数，但不能被看成是一个方法。

## 3.构造器实现编码【真实应用场景】