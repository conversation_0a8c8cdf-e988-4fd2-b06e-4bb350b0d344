// 构造器重载

type type_ChartParam = {
    width:number
    height:number
    color?:string
    radius?:number
}

/**
 * 计算正方形面积
 */
class Square {
    public width:number
    public height:number

    // 重载签名 1：接收两个数字参数 （函数重载声明）
    constructor(width:number, height:number) 
    // 重载签名 2：接收一个 type_ChartParam 类型的参数 （函数重载声明）
    constructor(chartParam:type_ChartParam)   
    // 实现签名，兼容所有重载签名
    constructor(arg1: number | type_ChartParam, arg2?: number) { 
        if (typeof arg1 === 'number' && typeof arg2 === 'number') {
            this.width = arg1;
            this.height = arg2;
        } else if (typeof arg1 === 'object' && arg1 !== null) {
            this.width = arg1.width;
            this.height = arg1.height;
        } else {
            // 处理其他意外情况，避免潜在错误
            throw new Error('Invalid constructor arguments');
        }
    }

    public getArea():number {
        return this.width * this.height
    }
}



let square = new Square(40,50)
console.log(square.getArea())



let type_ChartParam:type_ChartParam = {
    width:100,
    height:100,
}
let square2 = new Square(type_ChartParam)
console.log(square2.getArea())




