// 下面为你提供几个不同场景下 TypeScript 函数重载的示例，帮助你理解函数重载的使用方法。

// 示例 1：根据参数类型不同返回不同结果
// 该示例中，formatValue 函数能根据传入参数的类型，对其进行不同的格式化处理。



// 函数重载声明
function formatValue(value: number): string;
function formatValue(value: string): number;

// 函数实现
function formatValue(value: number | string): number | string {
    if (typeof value === 'number') {
        // 如果传入的是数字，将其格式化为字符串
        return value.toString();
    } else {
        // 如果传入的是字符串，尝试将其转换为数字
        return parseInt(value, 10);
    }
}

// 使用示例
const numResult = formatValue(123); // 返回 '123'
const strResult = formatValue('456'); // 返回 456



// 示例 2：根据参数数量不同执行不同逻辑
// createUserProfile 函数依据传入参数的数量，创建不同的用户资料对象。


// 定义用户资料类型
type UserProfile = {
    name: string;
    age?: number;
    email?: string;
};

// 函数重载声明
function createUserProfile(name: string): UserProfile;
function createUserProfile(name: string, age: number): UserProfile;
function createUserProfile(name: string, age: number, email: string): UserProfile;

// 函数实现
function createUserProfile(name: string, age?: number, email?: string): UserProfile {
    const profile: UserProfile = { name };
    if (age) {
        profile.age = age;
    }
    if (email) {
        profile.email = email;
    }
    return profile;
}

// 使用示例
const user1 = createUserProfile('Alice'); // 仅包含 name
const user2 = createUserProfile('Bob', 25); // 包含 name 和 age
const user3 = createUserProfile('Charlie', 30, '<EMAIL>'); // 包含 name、age 和 email

// 示例 3：结合泛型的函数重载
// getArrayItem 函数借助泛型，能从数组中获取指定索引的元素。

// 函数重载声明
function getArrayItem<T>(arr: T[], index: number): T;
function getArrayItem<T>(arr: T[], index: number, defaultValue: T): T;

// 函数实现
function getArrayItem<T>(arr: T[], index: number, defaultValue?: T): T {
    if (index >= 0 && index < arr.length) {
        return arr[index];
    }
    if (defaultValue !== undefined) {
        return defaultValue;
    }
    throw new Error('Index out of bounds');
}

// 使用示例
const numbers = [1, 2, 3];
const item1 = getArrayItem(numbers, 1); // 返回 2
const item2 = getArrayItem(numbers, 5, 0); // 返回默认值 0