type MessageType = 'text' | 'image' | 'video';

type Message = {
    id: number;
    type: MessageType;
    sendMessage: string;
}


let messages: Message[] = [
    {
        id: 1,
        type: 'text',
        sendMessage: 'hello world',
    },
    {
        id: 2,
        type: 'image',
        sendMessage:"http://wwww.ddddsjl.png"
    },
    {
        id: 3,
        type: 'video',
        sendMessage: 'http://loclahost/1.mp4',
    },
    {
        id: 4,
        type: 'image',
        sendMessage:"http://wwww.ddddsj2l.png"
    },
     {
        id: 5,
        type: 'image',
        sendMessage:"http://wwww.ddddsj2l22.png"
    },
];

// 函数重载声明（可以有多个函数重载声明）
function getMessage(id: number): Message | undefined;
function getMessage(type: MessageType, readRecordNumber: number): Message[];
// 函数实现
/**
 *  返回的数据类型可以不写，TS会自动推导
 * 
 * @param payload_frompage 
 * @param readRecordNumber 
 * @returns 
 */
function getMessage(payload_frompage: number | MessageType, readRecordNumber?: number): Message | Message[]  | undefined {
    if (typeof payload_frompage === 'number') { 
        // 如果传入的是数字，返回对应 ID 的消息
        return messages.find(message => message.id === payload_frompage);
    }
    else {
        // 如果传入的是消息类型，返回所有该类型的消息
        return messages.filter(message => message.type === payload_frompage).splice(0, readRecordNumber);
    }
}

// 使用示例
const messageById = getMessage(1); // 返回 { id: 1, type: 'text', sendMessage: 'hello world' }
console.log(messageById)

const messagesByType = getMessage('image', 2); // 返回 [{ id: 1, type: 'text', sendMessage: 'hello world' }]
console.log(messagesByType)

// 未使用函数重载
function getMessage1(payload_frompage: number | MessageType): Message | Message[] | undefined {
    if (typeof payload_frompage === 'number') {
        return messages.find(message => message.id === payload_frompage);
    }
    else {
        return messages.filter(message => message.type === payload_frompage);
    }
}

// TS没有办法运行之前根据传递的值来推导方法返回的数据类型
const messageById1 = getMessage1(1); // 返回 { id: 1, type: 'text', sendMessage: 'hello world' }
// console.log(messageById1.sendMessage) // 报错，因为 messageById1 的类型是 Message | undefined | Message[]，没有 sendMessage 属性

let messageById2 = <Message>getMessage1(1); // 只能通过类型断言来告诉 TS 返回的是 Message 类型


let x:unknown =3
let y:number = x //  报错，x 的类型是 unknown，不能赋值给 number 类型的变量 y