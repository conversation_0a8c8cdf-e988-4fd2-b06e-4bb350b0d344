/**
 *  订单详情的类。
 */
export default class OrderDetail {
    public orderDetailID: number = 0;
    public productName: string = ''; // 商品名称
    public quantity: number = 0; // 数量
    // public price: number = 0; // 价格
    public price!: number; // 价格 （非空断言） 告诉编译器，这个属性一定不会为空。  （1） 确保在使用该属性之前，已经对其进行了赋值。 （2） 告诉编译器，该属性不会为 null 或 undefined。


    constructor(orderDetailID_: number, productName_: string, quantity_: number, price_: number) {
        this.orderDetailID = orderDetailID_;
        this.productName = productName_;
        this.quantity = quantity_;
    }

    public getTotalPrice(): number {
        return this.quantity * this.price;
    }
}

let oderDetail = new OrderDetail(1, 'apple', 1, 10);
console.log(typeof oderDetail.price); // undefined
console.log(oderDetail.getTotalPrice()); // NaN