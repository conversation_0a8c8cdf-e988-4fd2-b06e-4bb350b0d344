import OrderDetail from "./oderDetail";

/**
 * 订单类
 */
class Order {
    // public orderID: number;
    // public date: Date;
    // public customerName: string;
    // public phone:string;

    // public orderDetails: Array<OrderDetail> = [];

    // constructor(orderID_: number, date_: Date, customerName_: string, phone_:string, orderDetails_: Array<OrderDetail>) {
    //     this.orderID = orderID_;
    //     this.date = date_;
    //     this.customerName = customerName_;
    //     this.phone = phone_; 
    //     this.orderDetails = orderDetails_;
    // }

    // 构建函数的简化写法 （不够直观）
    /**
     * 代码要点说明
        1.参数属性：在 TypeScript 里，可在构造函数参数前使用 public、private、protected 或 readonly 修饰符。这样做能同时声明并初始化类的属性。
        2.自动赋值：使用 public 修饰构造函数参数时，TypeScript 会自动创建同名的公共属性，并将传入的参数值赋给该属性，无需在构造函数体内手动赋值。
        3.构造函数体：当前构造函数体为空，因为属性的声明和初始化工作已由 TypeScript 自动完成。
        如果没有public、private、protected 或 readonly 修饰符，那么该属性将被视为普通的实例属性，需要在构造函数中进行显式赋值。
     * @param orderID 
     * @param date 
     * @param customerName 
     * @param phone 
     * @param orderDetails 
     */
    constructor(public orderID: number, public date: Date, public customerName: string, public phone: string, public orderDetails: Array<OrderDetail>) {
        
    }
}

let order = new Order(1, new Date(), 'zhangsan', '111', [
    new OrderDetail(1, 'apple', 1, 10),
    new OrderDetail(2, 'banana', 2, 20),
    new OrderDetail(3, 'orange', 3, 30),
])

console.log(order);

