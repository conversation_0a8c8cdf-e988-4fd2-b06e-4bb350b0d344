function QQUser(name, age, number, mark) {
  this.number = number;
  this.name = name;
  this.age = age;
  this.mark = mark;
  // 数组是引用类型（也是对象）
  this.commonfriends = ["小红", "小绿", "小黄"];
  // 函数也是一种引用类型
  this.show = function () {
    console.log(
      "我是" + this.name + "我今年" + this.age + "岁",
      "我的QQ号是" + this.number,
      "我的标签" + this.mark
    );
  };
}

// 对象也叫实例（instance）
// zhangsan是对象变量，是一个引用类型，对象是等号右边通过new关键字创建的一个实例，而且是运行期间才在队中开辟内存空间
var zhangsan = new QQUser("小明", 18, 123456, "男");
var lisi = new QQUser("小红", 18, 234567, "女");
var wangwu = new QQUser("小绿", 18, 345678, "女");
zhangsan.show();
lisi.show();
wangwu.show();
