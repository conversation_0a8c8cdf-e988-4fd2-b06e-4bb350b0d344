class Person {
    // 定义属性： 对象的属性=实例的变量=类的【非静态的】成员变量（属性）=简称成员变量（属性）
    public name: string;
    public age: number;
    public addresses: Array<string> = []; // 数组属性的引用类型
    // public resove: () => void = () => { };  函数属性的引用类型

    // 构造函数(构造器)
    constructor(name: string, age: number) {
        this.name = name;
        this.age = age;
    }
    sayHello() {
        console.log(`Hello, my name is ${this.name} and I am ${this.age} years old.`);
    }

    /**
     * 表示 Person 类的一个公共方法，用于描述人物正在吃某种食物的行为。
     * 该方法会在控制台输出人物正在吃指定食物的信息。
     * 
     * @param food - 一个字符串类型的参数，表示人物正在吃的食物名称。
     */
    public doEat(food: string): void {
        // 打印人物正在吃指定食物的信息
        console.log(`${this.name} is eating ${food}.`);
    }

    public play(): void {
        console.log(`${this.name} is playing.`);
    }
}

/**
 * 创建对象一共做了三件事
 * 1.在堆中为类的某个对象（实例）分配内存空间
 * 2.调用对应的构造函数
 * 3.将对象的地址赋值给对象变量（左侧的变量）
 */
// let zhangsanperson = new Person();
// // 给对象赋值的两种方式
// // 给对象赋值的第一种方式： 对象名.属性名 = 值
// zhangsanperson.name = 'zhangsan';
// zhangsanperson.age = 20;
// zhangsanperson.sayHello();

// 给对象赋值的第二种方式： 构造函数
let lisiPerson = new Person('lisi', 20);

console.log(lisiPerson);