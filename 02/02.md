
# 【第二章 第3节】深度透彻掌握原型【为深度掌握Typescript类，继承准备】-

## 11.为什么要用原型 【好处】
原型上所有的方法和属性都可以被构造函数【实际开发原型主要共享方法和所有实例公用引用属性】的实例共享，都为什么要共享呢? 先来看一个案例【先不用管什么是原型】

## 2.没有用原型会有什么问题?
总结问题:所有 QQUser 对象【也叫 QQUser 实例】都有相同的好友属性，好友属性用 commonfriends 英文表示，所有 QQuser 对象都有相同的 show 方法。但我们发现每一个 QQuser对象 【也叫 QQuser 实例】都单独分配一个comronfriends 属作空间和 show 方法空间,导致大量的空间浪费
答案:使用原型解决所有实例上的方法，还有所有实例上的共同属性都可以放到原型上去定义

```javascript
function QQUser(name, age, number, mark) {
    this.number = number
    this.name = name;
    this.age = age;
    this.mark = mark;
    this.commonfriends = ['小红', '小绿', '小黄'];
    this.show = function () {
        console.log('我是' + this.name + '我今年' + this.age + '岁');
    }
}

var user1 = new QQUser('小明', 18, 123456, '男');
var user2 = new QQUser('小红', 18, 123456, '女');
var user3 = new QQUser('小绿', 18, 123456, '女');
```



# 【第二章 第 4 节】深度透彻掌握原型 -2
## 3. 认识函数 + 原型定义
（1）函数也是一个对象，当真正开始执行函数，执行环境【开发时为浏览器或控制台】会为函数分配一个函数对象变量空间和函数对象空间，函数对象变量用函数名表示，存在栈空间中，函数对象空间是在堆中开辟的一个内存空间，这个空间中有一个默认的 prototype 属性，这个 prototype 属性就是一个原型对象属性【也叫对象变量】



（2）函数和构造函数的区别
当通过 new 函数 () 时，此刻这个函数就是构造函数【日后会演变成 TS 类的构造器】
（3）定义：原型【prototype ] 是定义函数由 JS 自动分配给函数的一个可以被所有构造函数实例对象变量共享的对象变量【也叫对象属性】



## 4. 如何访问原型对象空间上的属性和方法

（1）构造函数所有实例对象都可以访问型对象空间上的属性和方法【每一个实例都有默认的 proto 属性，这个 proto 属性指向原型对象空间】
(2)关于proto_:new 在创建新对象的时候，会赋予新对象一个属性指向构造函数的 prototype 属性，这个属性就是 proto
(3)可以直接通过构造函数.prototype对象属性来访问原型对象空间上的属性和方法

## 5.1 构造函数实例【也叫对象】如何访问原型对象空间上的属性和方法
(1)构造函数实例访问一个属性和方法，首先从实例空间中查找【当执行环境执行 new 构造函数()时，构造函数中通过 this 定义的属性和方法会分配在这个空间中】，如果找到该属性和方法，就停止查找，表示找到了;如果没有找到，就继续在该实例的原型对象空间中去查找该属性和方法【实例中默认的 proto 对象 属性指向原型对象空间】
(2)实例正是借助自身的_proto_对象属性 来査找原型对象空间中的属性和方法，有点像儿子去和爸爸要他没有的东西一样。讲到这里，这其实就是TS继承的原模型图】

## 5.2 增加或修改原型对象的属性或方法后，所有的实例对象立即可以访问的到 【但创建实例后再覆盖原型除外】

## 5.3 高频面试题:创建实例后再覆盖原型，实例对象无法访问到,为什么?
思考题:QQZhangSan.proto.show()和QQZhangsan.show()输出的结果完全一样吗? 为什么呢?

在 JavaScript 里，每个通过构造函数创建的实例对象都有一个 __proto__ 属性，该属性指向构造函数的 prototype 对象。当创建实例后再覆盖原型，实例对象就无法访问新原型上的属性和方法，下面详细解释原因。

### 原理说明
* 实例创建时的关联：使用 new 关键字调用构造函数创建实例时，实例的 __proto__ 属性会指向构造函数当时的 prototype 对象。这一关联在实例创建时就已确定。
* 原型覆盖的影响：后续覆盖构造函数的 prototype 属性，只是改变了构造函数新的 prototype 指向，但已经创建的实例的 __proto__ 属性依旧指向原来的 prototype 对象，不会随之改变。

```
function QQUser(name, age, number, mark) {
    this.number = number;
    this.name = name;
    this.age = age;
    this.mark = mark;
}

// 创建实例
var user1 = new QQUser('小明', 18, 123456, '男');

// 给原始原型添加方法
QQUser.prototype.show = function () {
    console.log('我是' + this.name + '我今年' + this.age + '岁');
};

// 覆盖原型
QQUser.prototype = {
    newShow: function () {
        console.log('新方法：我是' + this.name);
    }
};

// 创建新实例
var user2 = new QQUser('小红', 18, 123456, '女');

// user1 只能访问原始原型的方法
user1.show(); // 正常输出
// user1.newShow(); // 报错，user1 无法访问新原型的方法

// user2 可以访问新原型的方法
// user2.show(); // 报错，user2 无法访问原始原型的方法
user2.newShow(); // 正常输出
```
### 总结

* user1 在创建时，其 __proto__ 指向原始的 QQUser.prototype 对象。即便后续覆盖了 QQUser.prototype，user1.__proto__ 依旧指向原始原型，所以只能访问原始原型的方法。

* user2 是在原型覆盖后创建的，其 __proto__ 指向新的 QQUser.prototype 对象，因此能访问新原型的方法，却无法访问原始原型的方法。


## 6 Object.prototype

## 7.原型链+常见的五种原型继承



# 【第三章 第5节】
【TS自动重启+TS自动运行+Parcel自动打包】
## 步骤如下:
(1) 初始化
   npm init --yes 出现 package.json

(2) 安装typescript
    全局安装 cnpm i typescript-g 或
    本地安装:cnpm i typescript -D 或
    yarn安装 yarn global add typescript【cpm istall typescript -D 是cnpm install typescript --save-dev的缩写

(3) 生成tsconfig.json文件
    tsc --init

(4) 配置tsconfig.json文件 参考
    "outDir": "./dist",
    "rootDir": "./src",
    "module": "es6",
    "target": "es5",
    "sourceMap": true,
    "allowJs": true,
    "noImplicitAny": true,
    "strict": true,
    "noImplicitThis": true

(5) 编译 src 目录以及子目录下的 ts 文件
tsc【在 src 当前目录下：输入 tsc 注意直接写 tsc 命令即可】
【会把 src 目录以及子目录下的 ts 文件全部编译成 js 文件，并全部输出到 dist 目录中】

(6) 安装 ts-node
ts-node 让 node 能直接运行 ts 代码，无需使用 tsc 将 ts 代码编译成 js 代码。【ts-node 则包装了 node，它可以直接的运行 ts 代码】

全局安装 ：cnpm i ts-node -g
本地安装：cnpm i ts-node -D
yarn 安装: yarn global add ts-node

(6) 安装 nodemon 工具【自动检测工具】
nodemon 作用: 【nodemon 可以自动检测到目录中的文件更改时通过重新启动应用程序来调试基于 node.js 的应用程序】

全局安装：cnpm install -g nodemon
本地安装：cnpm i nodemon -D


(7) 在 package.json 中配置自动检测，自动重启应用程序

```javascript
“scripts”：{
“dev”："nodemon --watch src/ -e ts --exec ts-node ./src/app.ts"
}
```

nodemon --watch src/ 表示检测目录是 package.json 同级目录 src
-e ts 表示 nodemon 命令准备将要监听的是 ts 后缀的文件
--exec ts-node ./src/project/app.ts 表示检测到 src 目录下有任何变化，都要重新执行 app.ts 文件

### 2. Parcel 打包支持浏览器运行 TS 文件
(1) 安装 Parcel 打包工具: npm install parcel-bundler --save-dev
(2) 在 package.json 中给 npm 添加启动项，支持启动 parcel 工具包

```javascript
"scripts": {
    "dev": "parcel .index.html"
}
```
(3) 启动项目: npm run dev
```
(3) 启动项目: npm run dev
(4) 打开浏览器输入 URL_ADDRESS(4) 打开浏览器输入 http://localhost:1234/
(5) 项目中 src 目录下的 index.html 页面中引入 app.ts 文件

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script src="./app.ts"></script>
</body>
</html>
```

# 【第二章 第 6 节】用全栈的眼光带你更深入理解 TypeScript 类 【TS 类】
1. 学习 TypeScript 类的深远意义
相对以前 JavaScript 不得不用构造函数来充当 “类”，TypeScript 类的出现可以说是一次技术革命。让开发出来的项目尤其是大中项目的可读性好，可扩展性好了不是一点半点。

TypeScrip 类的出现完全改变了前端领域项目代码编写模式，配合 TypeScript 静态语言，编译期间就能检查语法错误的优势【项目上线后隐藏语法错误的风险几乎为零，相比不用 TypeScript 开发项目，使用 TypeScript 后对前端项目尤其是大中项目的开发，或底层第三方插件，组件库的开发带来的优势已经超乎了想象】。

TypeScript 类让前端开发人员开发和组织项目或阅读各大前端框架源码的思维问题的方式变得更先进，更全面了许多。因为类是 OOP【面型对象编程】的技术基石，OOP 思想来自于生活，更利于开发人员思考技术问题。TypeScript 类已经成了每次前端面试的高频面试考点。

在前端各大流行框架开发的项目中，比如 Vue3 项目，Angular 项目，基于 Antd UI 库的项目，还是后端 Nodejs 框架，比如：Nestjs，亦或是 Vue3 底层源码，都可以频频见到类的身影。

# 详解TS类底层源码（原生ES5语法）

```javascript
var Person = /** @class */ (function () {
  // 构造函数(构造器)
  function Person(name, age) {
    this.name = name;
    this.age = age;
  }
  Person.prototype.sayHello = function () {
    console.log(
      "Hello, my name is "
        .concat(this.name, " and I am ")
        .concat(this.age, " years old.")
    );
  };

  Person.prototype.doEat = function (food) {
    console.log("".concat(this.name, " is eating ").concat(food, "."));
  };
  Person.prototype.play = function () {
    console.log("".concat(this.name, " is playing."));
  };
  return Person;
})(); // 立即执行函数
```

# 2-8 深度掌握Typescript引用属性和它的真实应用场景


