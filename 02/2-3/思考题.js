// QQZhangSan._proto_.show() 和 QQZhangSan.show() 输出结果相同吗？

/**
 *
 *在 JavaScript 里，每个函数都有一个 prototype 属性，这是一个对象，被称作原型对象。
 *构造函数创建的所有实例都能共享原型对象上的属性和方法，这有助于节省内存，避免为每个实例重复创建相同的属性和方法。
 * 下面结合你之前编辑的 QQUser 构造函数举例说明
 */

// 定义 QQUser 构造函数
function QQUser(name, age, number, mark) {
  this.number = number;
  this.name = name;
  this.age = age;
  this.mark = mark;
}

// 在 QQUser 构造函数的原型对象上添加 commonfriends 属性和 show 方法
QQUser.prototype.commonfriends = ["小红", "小绿", "小黄"];
QQUser.prototype.show = function () {
  console.log(
    "我是" + this.name + "我今年" + this.age + "岁",
    "我的 QQ 号是" + this.number,
    "我的标签" + this.mark
  );
};

// 创建 QQUser 的实例
var QQZhangSan = new QQUser("小明", 18, 123456, "男");

// 使用 QQUser.prototype 访问 show 方法
QQZhangSan.show();
// 使用 QQZhangSan 实例的 __proto__ 属性访问 show 方法
QQZhangSan.__proto__.show();

// 验证这两种方法是否相同
console.log(QQZhangSan.show === QQZhangSan.__proto__.show); // 输出: true

console.log("QQUser prototype: ", QQUser.prototype);

// 答案不同
