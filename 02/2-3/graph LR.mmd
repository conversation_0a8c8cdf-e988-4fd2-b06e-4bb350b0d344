flowchart LR
    classDef obj fill:#f9f,stroke:#333,stroke-width:2px
    classDef proto fill:#9ff,stroke:#333,stroke-width:2px
    classDef func fill:#ff9,stroke:#333,stroke-width:2px

    subgraph "原型链关系"

        A([QQUser函数对象]):::func -->|prototype| B(QQUser.prototype对象):::proto
        B -->|constructor| A
        C([user1实例对象]):::obj -->|__proto__| B
        D([user2实例对象]):::obj -->|__proto__| B
        A -->|__proto__| E(Function.prototype对象):::proto
        B -->|__proto__| F(Object.prototype对象):::proto
        E -->|__proto__| F
    end