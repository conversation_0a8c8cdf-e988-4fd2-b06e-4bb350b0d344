/**
 *
 *在 JavaScript 里，每个函数都有一个 prototype 属性，这是一个对象，被称作原型对象。
 *构造函数创建的所有实例都能共享原型对象上的属性和方法，这有助于节省内存，避免为每个实例重复创建相同的属性和方法。
 * 下面结合你之前编辑的 QQUser 构造函数举例说明
 */

// 定义 QQUser 构造函数
function QQUser(name, age, number, mark) {
  this.number = number;
  this.name = name;
  this.age = age;
  this.mark = mark;
}

// 在 QQUser 构造函数的原型对象上添加 commonfriends 属性和 show 方法
QQUser.prototype.commonfriends = ["小红", "小绿", "小黄"];
QQUser.prototype.show = function () {
  console.log(
    "我是" + this.name + "我今年" + this.age + "岁",
    "我的 QQ 号是" + this.number,
    "我的标签" + this.mark
  );
};

// 创建 QQUser 的实例
var user1 = new QQUser("小明", 18, 123456, "男");
var user2 = new QQUser("小红", 18, 123456, "女");

// 访问实例的 commonfriends 属性和 show 方法
console.log(user1.commonfriends); // 输出: [ '小红', '小绿', '小黄' ]
user1.show(); // 输出: 我是小明我今年18岁 我的 QQ 号是123456 我的标签男

console.log(user2.commonfriends); // 输出: [ '小红', '小绿', '小黄' ]
user2.show(); // 输出: 我是小红我今年18岁 我的 QQ 号是123456 我的标签女

// 检查 user1 和 user2 的 commonfriends 是否指向同一个对象
console.log(user1.commonfriends === user2.commonfriends); // 输出: true

user1.commonfriends.push("小蓝");
console.log(user1.commonfriends);
console.log(user2.commonfriends);

// 验证 user1 的 __proto__ 属性
console.log(user1.__proto__ === QQUser.prototype); // 输出: true

// 使用标准方法验证
console.log(Object.getPrototypeOf(user1) === QQUser.prototype); //
