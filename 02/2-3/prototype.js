function QQUser(name, age, number, mark) {
  this.number = number;
  this.name = name;
  this.age = age;
  this.mark = mark;
  // 数组是引用类型（也是对象）
  this.commonfriends = ["小红", "小绿", "小黄"];
  // 函数也是一种引用类型
  this.show = function () {
    console.log(
      "我是" + this.name + "我今年" + this.age + "岁",
      "我的QQ号是" + this.number,
      "我的标签" + this.mark
    );
  };
}

// QQUser.prototype = {};
console.log("QQUser prototype: ", QQUser.prototype);

// QQUser.prototype = {
//   constructor: f QQUser(...),
//   __proto__: Object,
// };

let obj = { username: "<PERSON><PERSON><PERSON>", age: 18 };
console.log(obj);

let obj2 = obj;

obj = { address: "beijing" };
console.log("obj2: ", obj2);
console.log("obj: ", obj);
